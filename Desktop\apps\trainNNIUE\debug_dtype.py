#!/usr/bin/env python3

import torch
import torch.nn as nn
from qwe.chessnet_training import ChessNet, ChessDataset, parse_pgn_files
from torch.utils.data import DataLoader

def debug_dtypes():
    print("Debugging data types...")
    
    # Create a small dataset for testing
    print("Creating test dataset...")
    try:
        # Create a minimal dataset with dummy data
        positions = [
            ("rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1", "e2e4", "1-0"),
            ("rnbqkbnr/pppppppp/8/8/4P3/8/PPPP1PPP/RNBQKBNR b KQkq e3 0 1", "e7e5", "0-1"),
        ]
        
        dataset = ChessDataset(positions)
        dataloader = DataLoader(dataset, batch_size=2, shuffle=False)
        
        print("Dataset created successfully")
        
        # Test data loading
        print("Testing data loading...")
        for batch_idx, (boards, policies, values) in enumerate(dataloader):
            print(f"Batch {batch_idx}:")
            print(f"  Boards dtype: {boards.dtype}, shape: {boards.shape}")
            print(f"  Policies dtype: {policies.dtype}, shape: {policies.shape}")
            print(f"  Values dtype: {values.dtype}, shape: {values.shape}")
            
            # Test model
            print("Testing model...")
            model = ChessNet()
            model.float()  # Ensure float32
            
            print(f"Model parameter dtypes:")
            for name, param in model.named_parameters():
                print(f"  {name}: {param.dtype}")
                if param.dtype != torch.float32:
                    print(f"    WARNING: Parameter {name} is not float32!")
                break  # Just check first few
            
            # Test forward pass
            print("Testing forward pass...")
            model.eval()
            with torch.no_grad():
                try:
                    policy_pred, value_pred = model(boards.float())
                    print(f"  Policy prediction dtype: {policy_pred.dtype}")
                    print(f"  Value prediction dtype: {value_pred.dtype}")
                    print("Forward pass successful!")
                except Exception as e:
                    print(f"  Forward pass failed: {e}")
                    return
            
            # Test loss computation
            print("Testing loss computation...")
            model.train()
            try:
                criterion_policy = nn.CrossEntropyLoss()
                criterion_value = nn.MSELoss()
                
                policy_pred, value_pred = model(boards.float())
                
                print(f"  Before loss - Policy pred: {policy_pred.dtype}, targets: {policies.dtype}")
                print(f"  Before loss - Value pred: {value_pred.dtype}, targets: {values.dtype}")
                
                loss_policy = criterion_policy(policy_pred, policies)
                loss_value = criterion_value(value_pred, values.float())
                
                print(f"  Policy loss: {loss_policy.dtype}")
                print(f"  Value loss: {loss_value.dtype}")
                
                loss = loss_policy + loss_value
                print(f"  Total loss: {loss.dtype}")
                
                print("Testing backward pass...")
                loss.backward()
                print("Backward pass successful!")
                
            except Exception as e:
                print(f"  Loss computation failed: {e}")
                import traceback
                traceback.print_exc()
                return
            
            break  # Only test first batch
            
    except Exception as e:
        print(f"Debug failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_dtypes()
