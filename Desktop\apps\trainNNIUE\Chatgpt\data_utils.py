import chess.pgn
import numpy as np
import torch
from torch.utils.data import Dataset
from move_encoder import MoveEncoder

def board_to_tensor(board):
    planes = np.zeros((15, 8, 8), dtype=np.float32)
    piece_map = board.piece_map()
    for square, piece in piece_map.items():
        row, col = divmod(square, 8)
        idx = "pnbrqkPNBRQK".index(piece.symbol())
        planes[idx][row][col] = 1.0
    planes[12][:] = board.turn
    planes[13][:] = int(board.has_kingside_castling_rights(chess.WHITE)) + int(board.has_kingside_castling_rights(chess.BLACK))
    planes[14][:] = board.fullmove_number / 100.0
    return planes

class ChessDataset(Dataset):
    def __init__(self, pgn_path, move_encoder):
        self.positions = []
        self.labels = []
        self.encoder = move_encoder
        with open(pgn_path) as f:
            while True:
                game = chess.pgn.read_game(f)
                if game is None: break
                board = game.board()
                for move in game.mainline_moves():
                    input_tensor = board_to_tensor(board)
                    label = self.encoder.encode_move(move)
                    self.positions.append(input_tensor)
                    self.labels.append(label)
                    board.push(move)

    def __len__(self):
        return len(self.positions)

    def __getitem__(self, idx):
        return torch.tensor(self.positions[idx]), torch.tensor(self.labels[idx])