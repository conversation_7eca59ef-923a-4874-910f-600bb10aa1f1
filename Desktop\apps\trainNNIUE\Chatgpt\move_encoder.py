import chess

class MoveEncoder:
    def __init__(self):
        self.move2idx = {}
        self.idx2move = []

    def encode_move(self, move):
        uci = move.uci()
        if uci not in self.move2idx:
            self.move2idx[uci] = len(self.idx2move)
            self.idx2move.append(uci)
        return self.move2idx[uci]

    def decode_index(self, idx):
        return self.idx2move[idx] if idx < len(self.idx2move) else None

    def get_output_size(self):
        return len(self.idx2move)