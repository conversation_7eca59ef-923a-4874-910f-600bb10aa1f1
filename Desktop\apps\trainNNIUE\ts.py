import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import threading
import time
import os
import json
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.nn.parameter import Parameter
import math
import numpy as np
import glob

# Import the DSANNUE model from the original code
class DynamicSparseLinear(nn.Module):
    def __init__(self, in_features, out_features, sparsity=0.1):
        super().__init__()
        self.in_features = in_features
        self.out_features = out_features
        self.sparsity = sparsity
        
        # Learnable sparse connectivity mask
        self.mask = Parameter(torch.zeros(out_features, in_features))
        self.weight = Parameter(torch.randn(out_features, in_features))
        self.bias = Parameter(torch.zeros(out_features))
        
        # Initialize sparse mask
        with torch.no_grad():
            k = int(in_features * sparsity)
            for i in range(out_features):
                idx = torch.randperm(in_features)[:k]
                self.mask[i, idx] = 1
        
    def forward(self, x):
        # Apply sparse mask to weights
        masked_weight = self.weight * self.mask.sigmoid()  # Differentiable sparsity
        return F.linear(x, masked_weight, self.bias)

class ChessAttention(nn.Module):
    def __init__(self, piece_types=12):
        super().__init__()
        self.piece_types = piece_types
        
        # Rank, file and diagonal attention parameters
        self.rank_attn = Parameter(torch.randn(piece_types, 8))
        self.file_attn = Parameter(torch.randn(piece_types, 8))
        self.diag_attn = Parameter(torch.randn(piece_types, 15))
        
        # Dynamic bucketing
        self.bucket_proj = nn.Linear(3*64, 16)  # 16 buckets
        
    def forward(self, board):  # board: [batch, 12, 8, 8]
        batch_size = board.size(0)
        
        # Compute attention scores for each piece type
        rank_scores = torch.einsum('brc,pr->bprc', board, self.rank_attn)  # [B,12,8,8]
        file_scores = torch.einsum('brc,pf->bpfc', board, self.file_attn)  # [B,12,8,8]
        
        # Diagonal attention (pad and unfold)
        padded = F.pad(board, (7,7,7,7))  # Pad for all diagonals
        diags = []
        for i in range(15):
            diag = padded.narrow(2, i, 8).diagonal(offset=7-i, dim1=2, dim2=3)
            diags.append(diag)
        diag_scores = torch.einsum('bpd,pd->bpd', torch.stack(diags, -1), self.diag_attn)
        diag_scores = diag_scores.view(batch_size, self.piece_types, 8, 8)
        
        # Combine attention scores
        attn_scores = rank_scores + file_scores + diag_scores  # [B,12,8,8]
        
        # Top-k active squares per piece type
        topk_scores, topk_indices = torch.topk(
            attn_scores.flatten(2), k=8, dim=2)  # [B,12,8]
        
        # Create sparse input vector
        sparse_input = torch.zeros(batch_size, self.piece_types*64, 
                                  device=board.device)
        batch_idx = torch.arange(batch_size)[:, None, None]
        piece_idx = torch.arange(self.piece_types)[None, :, None]
        sparse_input[batch_idx, piece_idx*64 + topk_indices] = topk_scores.sigmoid()
        
        # Dynamic bucketing
        flat_attn = attn_scores.flatten(1)
        bucket_probs = F.softmax(self.bucket_proj(flat_attn), dim=-1)
        
        return sparse_input, bucket_probs

class DSANNUE(nn.Module):
    def __init__(self):
        super().__init__()
        
        # Input attention layer
        self.attention = ChessAttention()
        
        # Sparse first hidden layer
        self.sparse_layer = DynamicSparseLinear(12*64, 256, sparsity=0.0625)
        
        # Dynamic pattern layer
        self.conv1 = nn.Conv2d(1, 32, kernel_size=3, stride=2, padding=1)
        self.conv2 = nn.Conv2d(32, 32, kernel_size=3, stride=1, padding=1)
        self.conv_attn = nn.Linear(32*4*4, 4)  # Select top-4 filters
        
        # Output layers
        self.fc1 = nn.Linear(256 + 32*4 + 32, 32)
        self.fc2 = nn.Linear(32, 1)
        
        # Bucket embeddings
        self.bucket_emb = nn.Embedding(16, 32)
        
    def forward(self, board, incremental_update=None):
        if incremental_update is None:
            # Full forward pass
            sparse_input, bucket_probs = self.attention(board)
            
            # Sparse layer
            h1 = F.relu(self.sparse_layer(sparse_input))
            
            # Dynamic pattern layer
            batch_size = board.size(0)
            patterns = board.sum(dim=1, keepdim=True) # Sum over piece types for a single-channel image
            conv_feats = F.relu(self.conv1(patterns))
            conv_feats = F.relu(self.conv2(conv_feats))
            
            # Attention over filters
            filter_weights = F.softmax(self.conv_attn(conv_feats.flatten(1)), dim=-1)
            top_filters = torch.topk(filter_weights, k=4, dim=-1).indices
            selected_feats_list = []
            for i in range(batch_size):
                selected_feats_list.append(conv_feats[i, top_filters[i]].mean(dim=0))
            selected_feats = torch.stack(selected_feats_list).flatten(1)

            # Bucket features
            bucket_idx = torch.argmax(bucket_probs, dim=-1)
            bucket_feats = self.bucket_emb(bucket_idx)
            
            # Final output
            combined = torch.cat([h1, selected_feats, bucket_feats], dim=-1)
            h2 = F.relu(self.fc1(combined))
            return self.fc2(h2).squeeze(-1)
        else:
            # Incremental update implementation would go here
            raise NotImplementedError("Incremental update not implemented in this example")

class ChessTrainerGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Enhanced NNUE Chess Trainer")
        self.root.geometry("1200x800")
        
        # Initialize model and training state
        self.model = DSANNUE()
        self.optimizer = torch.optim.Adam(self.model.parameters()) # Initialize here
        self.training_thread = None
        self.is_training = False
        self.training_losses = []
        self.validation_losses = []
        self.epochs = []
        self.start_epoch = 0
        
        # Configuration variables
        self.config = {
            'epochs_per_file': 15,
            'batch_size': 2048,
            'learning_rate': 0.001,
            'weight_decay': 1e-5,
            'validation_interval': 5,
            'checkpoint_interval': 10,
            'scheduler_patience': 5,
            'early_stopping_patience': 2000,
            'save_epochs': [50, 70, 90, 100, 120, 150, 180, 200, 250, 300, 350, 400],
            'auto_save_best': False,
            'auto_load_checkpoint': True,
            'time_checkpoint_interval': 30,
            'enable_time_checkpoints': True,
            'constant_learning_rate': False
        }
        
        self.create_menu()
        self.create_notebook()
        
    def create_menu(self):
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Tools", menu=tools_menu)
        
    def create_notebook(self):
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Training tab
        self.training_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.training_frame, text='Training')
        self.create_training_tab()
        
        # Configuration tab
        self.config_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.config_frame, text='Configuration')
        self.create_configuration_tab()
        
        # Analysis tab
        self.analysis_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.analysis_frame, text='Analysis')
        self.create_analysis_tab()
        
    def create_training_tab(self):
        # File Selection section
        file_section = ttk.LabelFrame(self.training_frame, text="File Selection")
        file_section.pack(fill='x', padx=5, pady=5)
        
        # Training CSV Folder
        ttk.Label(file_section, text="Training CSV Folder:").grid(row=0, column=0, sticky='w', padx=5)
        self.train_folder_var = tk.StringVar()
        ttk.Entry(file_section, textvariable=self.train_folder_var, width=50).grid(row=0, column=1, padx=5)
        ttk.Button(file_section, text="Browse", command=self.browse_train_folder).grid(row=0, column=2, padx=5)
        
        # Processed Folder
        ttk.Label(file_section, text="Processed Folder:").grid(row=1, column=0, sticky='w', padx=5)
        self.processed_folder_var = tk.StringVar()
        ttk.Entry(file_section, textvariable=self.processed_folder_var, width=50).grid(row=1, column=1, padx=5)
        ttk.Button(file_section, text="Browse", command=self.browse_processed_folder).grid(row=1, column=2, padx=5)
        
        # Validation CSV
        ttk.Label(file_section, text="Validation CSV:").grid(row=2, column=0, sticky='w', padx=5)
        self.validation_file_var = tk.StringVar()
        ttk.Entry(file_section, textvariable=self.validation_file_var, width=50).grid(row=2, column=1, padx=5)
        ttk.Button(file_section, text="Browse", command=self.browse_validation_file).grid(row=2, column=2, padx=5)
        
        # Control buttons
        control_frame = ttk.Frame(self.training_frame)
        control_frame.pack(fill='x', padx=5, pady=5)
        
        self.start_button = ttk.Button(control_frame, text="Start Training", command=self.start_training)
        self.start_button.pack(side='left', padx=2)
        
        self.stop_button = ttk.Button(control_frame, text="Stop Training", command=self.stop_training, state='disabled')
        self.stop_button.pack(side='left', padx=2)
        
        ttk.Button(control_frame, text="Run Validation", command=self.run_validation).pack(side='left', padx=2)
        ttk.Button(control_frame, text="Load Latest Checkpoint", command=lambda: self.load_checkpoint()).pack(side='left', padx=2)
        ttk.Button(control_frame, text="Reset File Progress", command=self.reset_progress).pack(side='left', padx=2)
        
        # Progress section
        progress_section = ttk.LabelFrame(self.training_frame, text="Progress")
        progress_section.pack(fill='x', padx=5, pady=5)
        
        self.progress_label = ttk.Label(progress_section, text="Ready")
        self.progress_label.pack(anchor='w', padx=5)
        
        self.progress_bar = ttk.Progressbar(progress_section, mode='indeterminate')
        self.progress_bar.pack(fill='x', padx=5, pady=2)
        
        # Training Progress section
        training_progress_section = ttk.LabelFrame(self.training_frame, text="Training Progress")
        training_progress_section.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Create matplotlib figure for loss plots
        self.fig, (self.ax1, self.ax2) = plt.subplots(2, 1, figsize=(10, 6))
        self.fig.suptitle('Training and Validation Loss')
        
        self.ax1.set_title('Training Loss')
        self.ax1.set_xlabel('Epochs')
        self.ax1.set_ylabel('Loss')
        self.ax1.grid(True)
        self.ax1.set_xlim(0, 1)
        self.ax1.set_ylim(0, 1)
        
        self.ax2.set_title('Validation Loss')
        self.ax2.set_xlabel('Epochs')
        self.ax2.set_ylabel('Loss')
        self.ax2.grid(True)
        self.ax2.set_xlim(0, 1)
        self.ax2.set_ylim(0, 1)
        
        self.canvas = FigureCanvasTkAgg(self.fig, training_progress_section)
        self.canvas.get_tk_widget().pack(fill='both', expand=True)
        
    def create_configuration_tab(self):
        # Training Parameters section
        params_section = ttk.LabelFrame(self.config_frame, text="Training Parameters")
        params_section.pack(fill='x', padx=5, pady=5)
        
        # Create parameter entries
        param_vars = {}
        param_labels = [
            ('Epochs per file:', 'epochs_per_file'),
            ('Batch size:', 'batch_size'),
            ('Learning rate:', 'learning_rate'),
            ('Weight decay:', 'weight_decay'),
            ('Validation interval:', 'validation_interval'),
            ('Scheduler patience:', 'scheduler_patience'),
            ('Checkpoint interval:', 'checkpoint_interval'),
            ('Early stopping patience:', 'early_stopping_patience')
        ]
        
        for i, (label, key) in enumerate(param_labels):
            row = i // 2
            col = (i % 2) * 3
            
            ttk.Label(params_section, text=label).grid(row=row, column=col, sticky='w', padx=5, pady=2)
            var = tk.StringVar(value=str(self.config[key]))
            param_vars[key] = var
            ttk.Entry(params_section, textvariable=var, width=15).grid(row=row, column=col+1, padx=5, pady=2)
        
        self.param_vars = param_vars
        
        # Save model at epochs
        ttk.Label(params_section, text="Save model at epochs:").grid(row=4, column=0, sticky='w', padx=5, pady=2)
        self.save_epochs_var = tk.StringVar(value=','.join(map(str, self.config['save_epochs'])))
        ttk.Entry(params_section, textvariable=self.save_epochs_var, width=40).grid(row=4, column=1, columnspan=2, padx=5, pady=2)
        
        # Checkboxes
        checkbox_frame = ttk.Frame(params_section)
        checkbox_frame.grid(row=5, column=0, columnspan=6, sticky='w', padx=5, pady=5)
        
        self.auto_save_var = tk.BooleanVar(value=self.config['auto_save_best'])
        ttk.Checkbutton(checkbox_frame, text="Auto-save best model", variable=self.auto_save_var).grid(row=0, column=0, sticky='w')
        
        self.auto_load_var = tk.BooleanVar(value=self.config['auto_load_checkpoint'])
        ttk.Checkbutton(checkbox_frame, text="Auto-load latest checkpoint on start", variable=self.auto_load_var).grid(row=0, column=1, sticky='w', padx=20)
        
        self.time_checkpoint_var = tk.BooleanVar(value=self.config['enable_time_checkpoints'])
        ttk.Checkbutton(checkbox_frame, text="Enable time-based checkpoints", variable=self.time_checkpoint_var).grid(row=1, column=0, sticky='w')
        
        self.constant_lr_var = tk.BooleanVar(value=self.config['constant_learning_rate'])
        ttk.Checkbutton(checkbox_frame, text="Use constant learning rate (disable scheduler)", variable=self.constant_lr_var).grid(row=1, column=1, sticky='w', padx=20)
        
        # Time checkpoint interval
        ttk.Label(params_section, text="Time checkpoint interval (minutes):").grid(row=6, column=0, sticky='w', padx=5, pady=2)
        self.time_interval_var = tk.StringVar(value=str(self.config['time_checkpoint_interval']))
        ttk.Entry(params_section, textvariable=self.time_interval_var, width=15).grid(row=6, column=1, padx=5, pady=2)
        
        # Apply button
        ttk.Button(params_section, text="Apply Configuration", command=self.apply_configuration).grid(row=7, column=0, columnspan=6, pady=10)
        
        # Help text
        help_text = """
Epoch-based saving: Enter comma-separated epochs (e.g., 50,100,200,500)
Auto-save best: When enabled, automatically saves the model with best validation loss
Auto-load checkpoint: When enabled, automatically loads the latest checkpoint when starting training
Time checkpoints: Save checkpoints at regular time intervals (in minutes, 0 to disable)
Constant learning rate: When enabled, disables the learning rate scheduler
        """
        help_label = ttk.Label(self.config_frame, text=help_text, justify='left', foreground='gray')
        help_label.pack(anchor='w', padx=5, pady=5)
        
    def create_analysis_tab(self):
        # Position Evaluation section
        eval_section = ttk.LabelFrame(self.analysis_frame, text="Position Evaluation")
        eval_section.pack(fill='x', padx=5, pady=5)
        
        ttk.Label(eval_section, text="FEN:").grid(row=0, column=0, sticky='w', padx=5, pady=5)
        self.fen_var = tk.StringVar(value="rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1")
        ttk.Entry(eval_section, textvariable=self.fen_var, width=60).grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(eval_section, text="Evaluate", command=self.evaluate_position).grid(row=0, column=2, padx=5, pady=5)
        
        self.eval_result_var = tk.StringVar(value="Evaluation: Not calculated")
        ttk.Label(eval_section, textvariable=self.eval_result_var).grid(row=1, column=0, columnspan=3, sticky='w', padx=5, pady=5)
        
    def browse_train_folder(self):
        folder = filedialog.askdirectory()
        if folder:
            self.train_folder_var.set(folder)
            
    def browse_processed_folder(self):
        folder = filedialog.askdirectory()
        if folder:
            self.processed_folder_var.set(folder)
            
    def browse_validation_file(self):
        file = filedialog.askopenfilename(filetypes=[("CSV files", "*.csv")])
        if file:
            self.validation_file_var.set(file)
            
    def apply_configuration(self):
        try:
            # Update configuration from GUI
            for key, var in self.param_vars.items():
                if key in ['learning_rate', 'weight_decay']:
                    self.config[key] = float(var.get())
                else:
                    self.config[key] = int(var.get())
            
            # Parse save epochs
            epochs_str = self.save_epochs_var.get()
            self.config['save_epochs'] = [int(x.strip()) for x in epochs_str.split(',') if x.strip()]
            
            # Update boolean values
            self.config['auto_save_best'] = self.auto_save_var.get()
            self.config['auto_load_checkpoint'] = self.auto_load_var.get()
            self.config['enable_time_checkpoints'] = self.time_checkpoint_var.get()
            self.config['constant_learning_rate'] = self.constant_lr_var.get()
            self.config['time_checkpoint_interval'] = int(self.time_interval_var.get())
            
            # Re-initialize optimizer with new learning rate
            self.optimizer = torch.optim.Adam(self.model.parameters(), 
                                            lr=self.config['learning_rate'],
                                            weight_decay=self.config['weight_decay'])
            
            messagebox.showinfo("Success", "Configuration applied successfully!")
            
        except ValueError as e:
            messagebox.showerror("Error", f"Invalid configuration value: {e}")
            
    def start_training(self):
        if not self.train_folder_var.get():
            messagebox.showerror("Error", "Please select a training CSV folder")
            return
            
        self.is_training = True
        self.start_button.config(state='disabled')
        self.stop_button.config(state='normal')
        self.progress_bar.start()
        
        # Start training in a separate thread
        self.training_thread = threading.Thread(target=self.training_loop)
        self.training_thread.daemon = True
        self.training_thread.start()
        
    def stop_training(self):
        self.is_training = False
        self.start_button.config(state='normal')
        self.stop_button.config(state='disabled')
        self.progress_bar.stop()
        self.progress_label.config(text="Training stopped")
        
    def training_loop(self):
        """Simulate training process with checkpointing."""
        try:
            # Apply latest configuration and initialize optimizer
            self.apply_configuration()

            # Auto-load latest checkpoint if enabled
            if self.config['auto_load_checkpoint']:
                self.load_checkpoint()

            epoch = self.start_epoch
            while self.is_training and epoch < 1000:  # Max epochs for demo
                # Simulate training step
                time.sleep(0.1)  # Simulate training time
                
                # Generate dummy loss values for demonstration
                train_loss = 1.0 * np.exp(-epoch * 0.01) + 0.1 * np.random.random()
                
                if epoch % self.config['validation_interval'] == 0:
                    val_loss = 1.2 * np.exp(-epoch * 0.008) + 0.15 * np.random.random()
                    self.validation_losses.append(val_loss)
                
                self.training_losses.append(train_loss)
                self.epochs.append(epoch)
                
                # Update progress
                self.root.after(0, self.update_progress, epoch, train_loss)
                
                # Update plots
                if epoch % 5 == 0:  # Update plots every 5 epochs
                    self.root.after(0, self.update_plots)

                # Save checkpoint at specified interval
                if epoch > 0 and epoch % self.config['checkpoint_interval'] == 0:
                    self.save_checkpoint(epoch, train_loss)
                
                epoch += 1
                
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("Training Error", str(e)))
        finally:
            self.root.after(0, self.training_finished)
            
    def update_progress(self, epoch, loss):
        self.progress_label.config(text=f"Epoch {epoch}, Loss: {loss:.6f}")
        
    def update_plots(self):
        if not self.epochs:
            return
            
        # Clear and update training loss plot
        self.ax1.clear()
        self.ax1.plot(self.epochs, self.training_losses, 'b-', label='Training Loss')
        self.ax1.set_title('Training Loss')
        self.ax1.set_xlabel('Epochs')
        self.ax1.set_ylabel('Loss')
        self.ax1.grid(True)
        self.ax1.set_xlim(0, max(self.epochs) + 10 if self.epochs else 1)
        self.ax1.set_ylim(0, max(max(self.training_losses) * 1.1, 1.0) if self.training_losses else 1)
        
        # Clear and update validation loss plot
        self.ax2.clear()
        if self.validation_losses:
            val_epochs = [i * self.config['validation_interval'] for i in range(len(self.validation_losses))]
            self.ax2.plot(val_epochs, self.validation_losses, 'r-', label='Validation Loss')
        self.ax2.set_title('Validation Loss')
        self.ax2.set_xlabel('Epochs')
        self.ax2.set_ylabel('Loss')
        self.ax2.grid(True)
        self.ax2.set_xlim(0, max(self.epochs) + 10 if self.epochs else 1)
        self.ax2.set_ylim(0, max(max(self.validation_losses) * 1.1, 1.0) if self.validation_losses else 1)
        
        self.fig.tight_layout()
        self.canvas.draw()
        
    def training_finished(self):
        self.is_training = False
        self.start_button.config(state='normal')
        self.stop_button.config(state='disabled')
        self.progress_bar.stop()
        self.progress_label.config(text="Training completed")
        
    def run_validation(self):
        if not self.validation_file_var.get():
            messagebox.showerror("Error", "Please select a validation CSV file")
            return
        messagebox.showinfo("Validation", "Running validation... (This is a demo)")
        
    def save_checkpoint(self, epoch, loss):
        """Saves the model and optimizer state."""
        checkpoint_dir = 'checkpoints'
        if not os.path.exists(checkpoint_dir):
            os.makedirs(checkpoint_dir)
            
        filepath = os.path.join(checkpoint_dir, f'checkpoint_epoch_{epoch}.pth')
        
        state = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'training_losses': self.training_losses,
            'validation_losses': self.validation_losses,
            'config': self.config
        }
        
        torch.save(state, filepath)
        print(f"Checkpoint saved to {filepath}")
        self.progress_label.config(text=f"Epoch {epoch}, Loss: {loss:.6f} (Checkpoint saved)")

    def load_checkpoint(self, filepath=None):
        """Loads the model and optimizer state from a checkpoint."""
        if filepath is None:
            # Find the latest checkpoint
            checkpoint_dir = 'checkpoints'
            if not os.path.exists(checkpoint_dir):
                messagebox.showinfo("Checkpoint", "No checkpoint directory found. Starting from scratch.")
                return
            
            list_of_files = glob.glob(os.path.join(checkpoint_dir, '*.pth'))
            if not list_of_files:
                messagebox.showinfo("Checkpoint", "No checkpoints found. Starting from scratch.")
                return
            
            filepath = max(list_of_files, key=os.path.getctime)

        if not os.path.exists(filepath):
            messagebox.showerror("Error", f"Checkpoint file not found: {filepath}")
            return
        
        try:
            checkpoint = torch.load(filepath)
            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
            self.start_epoch = checkpoint['epoch'] + 1
            self.training_losses = checkpoint.get('training_losses', [])
            self.validation_losses = checkpoint.get('validation_losses', [])
            
            # Restore epochs list based on saved losses
            self.epochs = list(range(len(self.training_losses)))

            # Update plots and UI
            self.update_plots()
            self.progress_label.config(text=f"Resuming from Epoch {self.start_epoch -1}")
            messagebox.showinfo("Checkpoint Loaded", f"Successfully loaded checkpoint from {filepath}\nResuming from epoch {self.start_epoch}.")
        except Exception as e:
            messagebox.showerror("Checkpoint Error", f"Failed to load checkpoint: {e}")

    def reset_progress(self):
        self.training_losses.clear()
        self.validation_losses.clear()
        self.epochs.clear()
        self.start_epoch = 0
        self.update_plots()
        self.progress_label.config(text="Ready")
        messagebox.showinfo("Reset", "Training progress has been reset.")
        
    def fen_to_tensor(self, fen):
        """Converts a FEN string to a [1, 12, 8, 8] tensor."""
        piece_map = {
            'P': 0, 'N': 1, 'B': 2, 'R': 3, 'Q': 4, 'K': 5,
            'p': 6, 'n': 7, 'b': 8, 'r': 9, 'q': 10, 'k': 11
        }
        board_tensor = torch.zeros(1, 12, 8, 8)
        fen_parts = fen.split(' ')
        rows = fen_parts[0].split('/')
        
        for r, row_str in enumerate(rows):
            c = 0
            for char in row_str:
                if char.isdigit():
                    c += int(char)
                else:
                    piece_index = piece_map[char]
                    board_tensor[0, piece_index, r, c] = 1
                    c += 1
        return board_tensor

    def evaluate_position(self):
        fen = self.fen_var.get()
        if not fen:
            messagebox.showerror("Error", "Please enter a FEN string")
            return
            
        try:
            # Convert FEN to the format the model expects
            board_tensor = self.fen_to_tensor(fen)
            
            self.model.eval() # Set model to evaluation mode
            with torch.no_grad():
                evaluation = self.model(board_tensor)
                eval_score = evaluation.item()
                
            self.eval_result_var.set(f"Evaluation: {eval_score:.6f}")
            
        except Exception as e:
            messagebox.showerror("Evaluation Error", f"Error evaluating position: {e}")

def main():
    root = tk.Tk()
    app = ChessTrainerGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()