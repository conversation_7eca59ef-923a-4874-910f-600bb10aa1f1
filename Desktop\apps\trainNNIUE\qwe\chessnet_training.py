import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader

# Set default tensor type to float32 to avoid dtype issues
torch.set_default_dtype(torch.float32)
import chess
import chess.pgn
import numpy as np
import random
from collections import deque
import os
from tqdm import tqdm
import argparse
from datetime import datetime
import json

# ==================== CONFIGURATION ====================

class Config:
    # Model parameters
    INPUT_CHANNELS = 97  # 12 pieces * 8 history + 1 global
    BOARD_SIZE = 8
    HIDDEN_CHANNELS = 128
    NUM_RESIDUAL_BLOCKS = 20
    
    # Training parameters
    BATCH_SIZE = 2048
    LEARNING_RATE = 3e-4
    WEIGHT_DECAY = 1e-4
    EPOCHS = 100
    VALIDATION_SPLIT = 0.1
    
    # Data parameters
    MIN_ELO = 2600
    SAMPLE_RATE = 5  # Sample every 5th move
    MAX_POSITIONS = 10000000  # 10M positions
    
    # Paths
    PGN_DIR = "pgn_files/"
    MODEL_SAVE_PATH = "chessnet_model.pth"
    CHECKPOINT_DIR = "checkpoints/"
    LOG_DIR = "logs/"

    # Checkpoint settings
    SAVE_CHECKPOINT_EVERY = 5  # Save checkpoint every N epochs
    KEEP_LAST_N_CHECKPOINTS = 10  # Keep only last N checkpoints

# ==================== DATA PROCESSING ====================

class ChessDataset(Dataset):
    def __init__(self, positions):
        self.positions = positions
    
    def __len__(self):
        return len(self.positions)
    
    def __getitem__(self, idx):
        fen, move_uci, result = self.positions[idx]
        board_tensor = self.fen_to_tensor(fen)
        policy_target = self.move_to_index(move_uci)
        value_target = self.result_to_value(result)

        # Ensure all tensors are float32
        return board_tensor.float(), torch.tensor(policy_target, dtype=torch.long), torch.tensor(value_target, dtype=torch.float32)
    
    def fen_to_tensor(self, fen):
        """Convert FEN to 8x8x97 tensor"""
        board = chess.Board(fen)
        tensor = np.zeros((Config.INPUT_CHANNELS, 8, 8), dtype=np.float32)
        
        # Piece planes (12 planes for current position)
        piece_map = board.piece_map()
        for square, piece in piece_map.items():
            row, col = divmod(square, 8)
            # Flip row for proper orientation
            row = 7 - row
            piece_idx = (piece.piece_type - 1) * 2 + (0 if piece.color == chess.WHITE else 1)
            tensor[piece_idx, row, col] = 1.0
        
        # Global state plane
        global_features = np.array([
            1.0 if board.turn == chess.WHITE else 0.0,  # Whose turn
            1.0 if board.has_castling_rights(chess.WHITE) and 'K' in board.fen() else 0.0,  # White kingside
            1.0 if board.has_castling_rights(chess.WHITE) and 'Q' in board.fen() else 0.0,  # White queenside
            1.0 if board.has_castling_rights(chess.BLACK) and 'k' in board.fen() else 0.0,  # Black kingside
            1.0 if board.has_castling_rights(chess.BLACK) and 'q' in board.fen() else 0.0,  # Black queenside
            board.halfmove_clock / 100.0,  # Halfmove clock
            board.fullmove_number / 200.0,  # Fullmove number
        ], dtype=np.float32)
        
        # Broadcast global features to all positions
        for i in range(7):
            tensor[96 - i, :, :] = global_features[i]
        
        return torch.FloatTensor(tensor)
    
    def move_to_index(self, move_uci):
        """Convert UCI move to index (0-4095)"""
        try:
            from_square = chess.SQUARE_NAMES.index(move_uci[:2])
            to_square = chess.SQUARE_NAMES.index(move_uci[2:4])
            return from_square * 64 + to_square
        except:
            return 0  # Default move
    
    def result_to_value(self, result):
        """Convert game result to value [-1, 1]"""
        if result == "1-0":
            return 1.0
        elif result == "0-1":
            return -1.0
        else:  # Draw
            return 0.0

def parse_pgn_files(pgn_dir, max_positions=Config.MAX_POSITIONS):
    """Parse PGN files and extract training positions"""
    positions = []
    
    for filename in os.listdir(pgn_dir):
        if filename.endswith(".pgn"):
            filepath = os.path.join(pgn_dir, filename)
            print(f"Parsing {filename}...")
            
            with open(filepath) as pgn_file:
                while len(positions) < max_positions:
                    game = chess.pgn.read_game(pgn_file)
                    if game is None:
                        break
                    
                    # Check if game meets ELO criteria
                    white_elo = game.headers.get("WhiteElo", "0")
                    black_elo = game.headers.get("BlackElo", "0")
                    
                    try:
                        if int(white_elo) < Config.MIN_ELO or int(black_elo) < Config.MIN_ELO:
                            continue
                    except:
                        continue
                    
                    result = game.headers.get("Result", "*")
                    board = game.board()
                    move_count = 0
                    
                    for move in game.mainline_moves():
                        if move_count % Config.SAMPLE_RATE == 0 and len(positions) < max_positions:
                            fen = board.fen()
                            uci_move = move.uci()
                            
                            # Check if move is legal
                            if board.is_legal(move):
                                positions.append((fen, uci_move, result))
                        
                        board.push(move)
                        move_count += 1
    
    return positions

# ==================== MODEL ARCHITECTURE ====================

class ConvBlock(nn.Module):
    def __init__(self, in_channels, out_channels, kernel_size=3, stride=1, padding=1):
        super(ConvBlock, self).__init__()
        self.conv = nn.Conv2d(in_channels, out_channels, kernel_size, stride, padding)
        self.bn = nn.BatchNorm2d(out_channels)
        self.relu = nn.ReLU(inplace=True)
    
    def forward(self, x):
        return self.relu(self.bn(self.conv(x)))

class ResidualBlock(nn.Module):
    def __init__(self, channels):
        super(ResidualBlock, self).__init__()
        self.conv1 = nn.Conv2d(channels, channels, 3, padding=1)
        self.bn1 = nn.BatchNorm2d(channels)
        self.conv2 = nn.Conv2d(channels, channels, 3, padding=1)
        self.bn2 = nn.BatchNorm2d(channels)
        self.relu = nn.ReLU(inplace=True)
    
    def forward(self, x):
        residual = x
        out = self.relu(self.bn1(self.conv1(x)))
        out = self.bn2(self.conv2(out))
        out += residual
        out = self.relu(out)
        return out

class CBAM(nn.Module):
    """Convolutional Block Attention Module"""
    def __init__(self, channels, reduction=16):
        super(CBAM, self).__init__()
        # Channel attention
        self.channel_gate = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(channels, channels // reduction, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels // reduction, channels, 1),
            nn.Sigmoid()
        )
        
        # Spatial attention
        self.spatial_gate = nn.Sequential(
            nn.Conv2d(channels, 1, 7, padding=3),
            nn.Sigmoid()
        )
    
    def forward(self, x):
        # Channel attention
        channel_att = self.channel_gate(x)
        x = x * channel_att
        
        # Spatial attention
        spatial_att = self.spatial_gate(x)
        x = x * spatial_att
        
        return x

class ChessNet(nn.Module):
    def __init__(self):
        super(ChessNet, self).__init__()
        
        # Initial convolution
        self.conv_input = ConvBlock(Config.INPUT_CHANNELS, Config.HIDDEN_CHANNELS)
        
        # Residual tower
        self.residual_blocks = nn.ModuleList()
        self.cbam_blocks = nn.ModuleList()
        
        for i in range(Config.NUM_RESIDUAL_BLOCKS):
            self.residual_blocks.append(ResidualBlock(Config.HIDDEN_CHANNELS))
            if (i + 1) % 5 == 0:  # Add CBAM every 5 blocks
                self.cbam_blocks.append(CBAM(Config.HIDDEN_CHANNELS))
        
        # Policy head (8x8x8x8 = 4096 possible moves)
        self.policy_conv = nn.Conv2d(Config.HIDDEN_CHANNELS, 2, 1)
        self.policy_fc = nn.Linear(2 * 8 * 8, 64)
        self.policy_output = nn.Linear(64, 4096)  # 8x8x8x8
        
        # Value head
        self.value_conv = nn.Conv2d(Config.HIDDEN_CHANNELS, 1, 1)
        self.value_fc1 = nn.Linear(8 * 8, 64)
        self.value_fc2 = nn.Linear(64, 1)
        
        self.relu = nn.ReLU(inplace=True)
        self.tanh = nn.Tanh()
    
    def forward(self, x):
        # Initial convolution
        x = self.conv_input(x)
        
        # Residual tower with CBAM
        cbam_idx = 0
        for i, block in enumerate(self.residual_blocks):
            x = block(x)
            if (i + 1) % 5 == 0 and cbam_idx < len(self.cbam_blocks):
                x = self.cbam_blocks[cbam_idx](x)
                cbam_idx += 1
        
        # Policy head
        policy = self.relu(self.policy_conv(x))
        policy = policy.view(policy.size(0), -1)
        policy = self.relu(self.policy_fc(policy))
        policy = self.policy_output(policy)
        policy = F.softmax(policy, dim=1)
        
        # Value head
        value = self.relu(self.value_conv(x))
        value = value.view(value.size(0), -1)
        value = self.relu(self.value_fc1(value))
        value = self.tanh(self.value_fc2(value))
        
        return policy, value.squeeze()

# ==================== TRAINING LOOP ====================

class ChessTrainer:
    def __init__(self, model, train_loader, val_loader, config):
        self.model = model
        self.train_loader = train_loader
        self.val_loader = val_loader
        self.config = config
        
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.model.to(self.device)
        
        self.optimizer = optim.AdamW(
            self.model.parameters(),
            lr=config.LEARNING_RATE,
            weight_decay=config.WEIGHT_DECAY
        )
        
        self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
            self.optimizer, T_max=config.EPOCHS
        )
        
        self.criterion_policy = nn.CrossEntropyLoss(label_smoothing=0.1)
        self.criterion_value = nn.MSELoss()
        
        # Logging
        self.train_losses = []
        self.val_losses = []
        self.train_accuracies = []
        self.val_accuracies = []
        
        # Create log directory
        os.makedirs(config.LOG_DIR, exist_ok=True)
    
    def train_epoch(self):
        self.model.train()
        total_loss = 0
        correct_policy = 0
        total_policy = 0
        
        pbar = tqdm(self.train_loader, desc="Training")
        for batch_idx, (boards, policies, values) in enumerate(pbar):
            boards, policies, values = boards.to(self.device), policies.to(self.device), values.to(self.device)
            
            self.optimizer.zero_grad()
            
            policy_pred, value_pred = self.model(boards)
            
            # Apply legality mask (simplified - in practice, you'd compute legal moves)
            # For now, we'll just use the provided policy indices
            
            loss_policy = self.criterion_policy(policy_pred, policies)
            loss_value = self.criterion_value(value_pred, values)
            
            loss = loss_policy + loss_value
            loss.backward()
            
            # Gradient clipping
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            self.optimizer.step()
            
            total_loss += loss.item()
            
            # Calculate accuracy
            _, predicted = policy_pred.max(1)
            correct_policy += predicted.eq(policies).sum().item()
            total_policy += policies.size(0)
            
            pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Acc': f'{100.*correct_policy/total_policy:.2f}%'
            })
        
        avg_loss = total_loss / len(self.train_loader)
        accuracy = 100. * correct_policy / total_policy
        
        return avg_loss, accuracy
    
    def validate(self):
        self.model.eval()
        total_loss = 0
        correct_policy = 0
        total_policy = 0
        
        with torch.no_grad():
            for boards, policies, values in self.val_loader:
                boards, policies, values = boards.to(self.device), policies.to(self.device), values.to(self.device)
                
                policy_pred, value_pred = self.model(boards)
                
                loss_policy = self.criterion_policy(policy_pred, policies)
                loss_value = self.criterion_value(value_pred, values)
                loss = loss_policy + loss_value
                
                total_loss += loss.item()
                
                _, predicted = policy_pred.max(1)
                correct_policy += predicted.eq(policies).sum().item()
                total_policy += policies.size(0)
        
        avg_loss = total_loss / len(self.val_loader)
        accuracy = 100. * correct_policy / total_policy
        
        return avg_loss, accuracy
    
    def train(self):
        best_val_loss = float('inf')
        
        for epoch in range(self.config.EPOCHS):
            print(f"\nEpoch {epoch+1}/{self.config.EPOCHS}")
            
            # Training
            train_loss, train_acc = self.train_epoch()
            
            # Validation
            val_loss, val_acc = self.validate()
            
            # Learning rate scheduling
            self.scheduler.step()
            
            # Logging
            self.train_losses.append(train_loss)
            self.val_losses.append(val_loss)
            self.train_accuracies.append(train_acc)
            self.val_accuracies.append(val_acc)
            
            print(f"Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.2f}%")
            print(f"Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.2f}%")
            
            # Check if this is the best model
            is_best = val_loss < best_val_loss
            if is_best:
                best_val_loss = val_loss
                print(f"New best model with val loss: {val_loss:.4f}")

            # Save checkpoint (periodic or best)
            should_save_checkpoint = (
                epoch % self.config.SAVE_CHECKPOINT_EVERY == 0 or
                is_best or
                epoch == self.config.EPOCHS  # Always save last epoch
            )

            if should_save_checkpoint:
                self.save_checkpoint(epoch, val_loss, is_best)

            # Save logs
            self.save_logs(epoch)
    
    def save_logs(self, epoch):
        log_data = {
            'epoch': epoch,
            'train_losses': self.train_losses,
            'val_losses': self.val_losses,
            'train_accuracies': self.train_accuracies,
            'val_accuracies': self.val_accuracies,
            'timestamp': datetime.now().isoformat()
        }

        os.makedirs(self.config.LOG_DIR, exist_ok=True)
        log_file = os.path.join(self.config.LOG_DIR, f'training_log_epoch_{epoch}.json')
        with open(log_file, 'w') as f:
            json.dump(log_data, f, indent=2)

    def save_checkpoint(self, epoch, val_loss, is_best=False):
        """Save comprehensive checkpoint with model, optimizer, and training state"""
        os.makedirs(self.config.CHECKPOINT_DIR, exist_ok=True)

        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict() if hasattr(self, 'scheduler') else None,
            'train_losses': self.train_losses,
            'val_losses': self.val_losses,
            'train_accuracies': self.train_accuracies,
            'val_accuracies': self.val_accuracies,
            'best_val_loss': val_loss,
            'config': {
                'learning_rate': self.config.LEARNING_RATE,
                'batch_size': self.config.BATCH_SIZE,
                'weight_decay': self.config.WEIGHT_DECAY,
            },
            'timestamp': datetime.now().isoformat(),
            'pytorch_version': torch.__version__
        }

        # Save regular checkpoint
        checkpoint_path = os.path.join(self.config.CHECKPOINT_DIR, f'checkpoint_epoch_{epoch:03d}.pth')
        torch.save(checkpoint, checkpoint_path)
        print(f"Checkpoint saved: {checkpoint_path}")

        # Save best model separately
        if is_best:
            best_path = os.path.join(self.config.CHECKPOINT_DIR, 'best_model.pth')
            torch.save(checkpoint, best_path)
            # Also save just the model state dict for easy loading
            torch.save(self.model.state_dict(), self.config.MODEL_SAVE_PATH)
            print(f"Best model saved: {best_path}")

        # Save latest checkpoint link
        latest_path = os.path.join(self.config.CHECKPOINT_DIR, 'latest_checkpoint.pth')
        torch.save(checkpoint, latest_path)

        # Cleanup old checkpoints
        self.cleanup_old_checkpoints()

    def cleanup_old_checkpoints(self):
        """Remove old checkpoints, keeping only the last N"""
        if not os.path.exists(self.config.CHECKPOINT_DIR):
            return

        checkpoint_files = []
        for file in os.listdir(self.config.CHECKPOINT_DIR):
            if file.startswith('checkpoint_epoch_') and file.endswith('.pth'):
                epoch_num = int(file.split('_')[2].split('.')[0])
                checkpoint_files.append((epoch_num, file))

        # Sort by epoch number and keep only the last N
        checkpoint_files.sort(key=lambda x: x[0])
        if len(checkpoint_files) > self.config.KEEP_LAST_N_CHECKPOINTS:
            files_to_remove = checkpoint_files[:-self.config.KEEP_LAST_N_CHECKPOINTS]
            for _, filename in files_to_remove:
                file_path = os.path.join(self.config.CHECKPOINT_DIR, filename)
                try:
                    os.remove(file_path)
                    print(f"Removed old checkpoint: {filename}")
                except OSError as e:
                    print(f"Warning: Could not remove {filename}: {e}")

    def load_checkpoint(self, checkpoint_path):
        """Load checkpoint and restore training state"""
        if not os.path.exists(checkpoint_path):
            print(f"Checkpoint not found: {checkpoint_path}")
            return False

        try:
            checkpoint = torch.load(checkpoint_path, map_location='cpu')

            # Load model state
            self.model.load_state_dict(checkpoint['model_state_dict'])

            # Load optimizer state
            if 'optimizer_state_dict' in checkpoint:
                self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])

            # Load scheduler state if available
            if hasattr(self, 'scheduler') and 'scheduler_state_dict' in checkpoint and checkpoint['scheduler_state_dict']:
                self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])

            # Restore training history
            self.train_losses = checkpoint.get('train_losses', [])
            self.val_losses = checkpoint.get('val_losses', [])
            self.train_accuracies = checkpoint.get('train_accuracies', [])
            self.val_accuracies = checkpoint.get('val_accuracies', [])

            start_epoch = checkpoint.get('epoch', 0) + 1
            best_val_loss = checkpoint.get('best_val_loss', float('inf'))

            print(f"Checkpoint loaded successfully from epoch {checkpoint.get('epoch', 0)}")
            print(f"Resuming training from epoch {start_epoch}")
            print(f"Best validation loss so far: {best_val_loss:.4f}")

            return True, start_epoch, best_val_loss

        except Exception as e:
            print(f"Error loading checkpoint: {e}")
            return False

    @staticmethod
    def find_latest_checkpoint(checkpoint_dir):
        """Find the latest checkpoint file"""
        if not os.path.exists(checkpoint_dir):
            return None

        latest_path = os.path.join(checkpoint_dir, 'latest_checkpoint.pth')
        if os.path.exists(latest_path):
            return latest_path

        # Fallback: find the highest numbered checkpoint
        checkpoint_files = []
        for file in os.listdir(checkpoint_dir):
            if file.startswith('checkpoint_epoch_') and file.endswith('.pth'):
                try:
                    epoch_num = int(file.split('_')[2].split('.')[0])
                    checkpoint_files.append((epoch_num, file))
                except (IndexError, ValueError):
                    continue

        if checkpoint_files:
            checkpoint_files.sort(key=lambda x: x[0], reverse=True)
            return os.path.join(checkpoint_dir, checkpoint_files[0][1])

        return None

    @staticmethod
    def get_checkpoint_info(checkpoint_path):
        """Get information about a checkpoint without loading it fully"""
        try:
            checkpoint = torch.load(checkpoint_path, map_location='cpu')
            return {
                'epoch': checkpoint.get('epoch', 0),
                'best_val_loss': checkpoint.get('best_val_loss', float('inf')),
                'timestamp': checkpoint.get('timestamp', 'Unknown'),
                'pytorch_version': checkpoint.get('pytorch_version', 'Unknown'),
                'config': checkpoint.get('config', {})
            }
        except Exception as e:
            return {'error': str(e)}

# ==================== DATA AUGMENTATION ====================

def augment_board_tensor(tensor):
    """Apply random rotations and flips for data augmentation"""
    # Convert to numpy for easier manipulation
    board_np = tensor.numpy()
    
    # Random rotation (0, 90, 180, 270 degrees)
    k = random.randint(0, 3)
    if k > 0:
        board_np = np.rot90(board_np, k, axes=(1, 2))
    
    # Random flip (horizontal)
    if random.random() > 0.5:
        board_np = np.flip(board_np, axis=2)
    
    return torch.FloatTensor(board_np)

# ==================== STANDALONE TRAINING FUNCTIONS ====================

def train_epoch(model, train_loader, optimizer, device):
    """Standalone training function for one epoch"""
    model.train()
    model.float()  # Ensure model is float32
    total_loss = 0
    correct_policy = 0
    total_policy = 0

    criterion_policy = torch.nn.CrossEntropyLoss(label_smoothing=0.1)
    criterion_value = torch.nn.MSELoss()

    pbar = tqdm(train_loader, desc="Training")
    for batch_idx, (boards, policies, values) in enumerate(pbar):
        # Ensure all tensors are float32
        boards = boards.float().to(device)
        policies = policies.long().to(device)  # CrossEntropyLoss expects long
        values = values.float().to(device)

        optimizer.zero_grad()

        policy_pred, value_pred = model(boards)

        loss_policy = criterion_policy(policy_pred, policies)
        loss_value = criterion_value(value_pred, values)

        loss = loss_policy + loss_value
        loss.backward()

        # Gradient clipping
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)

        optimizer.step()

        total_loss += loss.item()

        # Calculate accuracy
        _, predicted = policy_pred.max(1)
        correct_policy += predicted.eq(policies).sum().item()
        total_policy += policies.size(0)

        pbar.set_postfix({
            'Loss': f'{loss.item():.4f}',
            'Acc': f'{100.*correct_policy/total_policy:.2f}%'
        })

    avg_loss = total_loss / len(train_loader)
    return avg_loss

def validate(model, val_loader, device):
    """Standalone validation function"""
    model.eval()
    model.float()  # Ensure model is float32
    total_loss = 0
    correct_policy = 0
    total_policy = 0

    criterion_policy = torch.nn.CrossEntropyLoss(label_smoothing=0.1)
    criterion_value = torch.nn.MSELoss()

    with torch.no_grad():
        for boards, policies, values in val_loader:
            # Ensure all tensors are float32
            boards = boards.float().to(device)
            policies = policies.long().to(device)  # CrossEntropyLoss expects long
            values = values.float().to(device)

            policy_pred, value_pred = model(boards)

            loss_policy = criterion_policy(policy_pred, policies)
            loss_value = criterion_value(value_pred, values)
            loss = loss_policy + loss_value

            total_loss += loss.item()

            _, predicted = policy_pred.max(1)
            correct_policy += predicted.eq(policies).sum().item()
            total_policy += policies.size(0)

    avg_loss = total_loss / len(val_loader)
    return avg_loss

# ==================== MAIN TRAINING SCRIPT ====================

def main():
    # Parse arguments
    parser = argparse.ArgumentParser(description='Train ChessNet from PGN data')
    parser.add_argument('--pgn_dir', type=str, default=Config.PGN_DIR,
                       help='Directory containing PGN files')
    parser.add_argument('--batch_size', type=int, default=Config.BATCH_SIZE,
                       help='Batch size for training')
    parser.add_argument('--epochs', type=int, default=Config.EPOCHS,
                       help='Number of training epochs')
    parser.add_argument('--lr', type=float, default=Config.LEARNING_RATE,
                       help='Learning rate')
    
    args = parser.parse_args()
    
    # Update config with arguments
    Config.BATCH_SIZE = args.batch_size
    Config.EPOCHS = args.epochs
    Config.LEARNING_RATE = args.lr
    Config.PGN_DIR = args.pgn_dir
    
    print("Parsing PGN files...")
    positions = parse_pgn_files(Config.PGN_DIR, Config.MAX_POSITIONS)
    print(f"Loaded {len(positions)} positions")
    
    # Split into train/val
    split_idx = int(len(positions) * (1 - Config.VALIDATION_SPLIT))
    train_positions = positions[:split_idx]
    val_positions = positions[split_idx:]
    
    print(f"Train set: {len(train_positions)} positions")
    print(f"Validation set: {len(val_positions)} positions")
    
    # Create datasets
    train_dataset = ChessDataset(train_positions)
    val_dataset = ChessDataset(val_positions)
    
    # Create data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=Config.BATCH_SIZE,
        shuffle=True,
        num_workers=4,
        pin_memory=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=Config.BATCH_SIZE,
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    # Create model
    model = ChessNet()
    print(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # Create trainer
    trainer = ChessTrainer(model, train_loader, val_loader, Config)
    
    # Start training
    print("Starting training...")
    trainer.train()
    
    print("Training completed!")
    print(f"Best model saved to: {Config.MODEL_SAVE_PATH}")

if __name__ == "__main__":
    main()