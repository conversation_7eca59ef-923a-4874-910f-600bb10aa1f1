#!/usr/bin/env python3

import tkinter as tk
from tkinter import ttk, messagebox

# Simple test to verify the GUI can start without errors
class SimpleTestGUI(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("Simple Test GUI")
        self.geometry("400x300")
        
        # Create a simple interface
        frame = tk.Frame(self)
        frame.pack(pady=20, padx=20, fill=tk.BOTH, expand=True)
        
        # Store button references
        self.start_button = tk.Button(frame, text="Start", command=self.start_action)
        self.start_button.pack(pady=5)
        
        self.stop_button = tk.Button(frame, text="Stop", command=self.stop_action, state=tk.DISABLED)
        self.stop_button.pack(pady=5)
        
        self.status_label = tk.Label(frame, text="Ready")
        self.status_label.pack(pady=10)
        
    def start_action(self):
        self.status_label.config(text="Started")
        self.update_controls(is_running=True)
        
    def stop_action(self):
        self.status_label.config(text="Stopped")
        self.update_controls(is_running=False)
        
    def update_controls(self, is_running):
        # This should work without KeyError
        self.start_button.config(state=tk.DISABLED if is_running else tk.NORMAL)
        self.stop_button.config(state=tk.NORMAL if is_running else tk.DISABLED)

if __name__ == "__main__":
    try:
        app = SimpleTestGUI()
        print("Simple GUI started successfully")
        app.mainloop()
    except Exception as e:
        print(f"GUI failed to start: {e}")
        import traceback
        traceback.print_exc()
