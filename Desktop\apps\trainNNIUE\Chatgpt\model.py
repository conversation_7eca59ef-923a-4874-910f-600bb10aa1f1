import torch
import torch.nn as nn
import torch.nn.functional as F

class SelfAttention(nn.Module):
    def __init__(self, embed_size):
        super().__init__()
        self.qkv = nn.Linear(embed_size, embed_size * 3)
        self.fc_out = nn.Linear(embed_size, embed_size)

    def forward(self, x):
        q, k, v = self.qkv(x).chunk(3, dim=-1)
        attn = torch.softmax((q @ k.transpose(-2, -1)) / (x.size(-1) ** 0.5), dim=-1)
        out = attn @ v
        return self.fc_out(out)

class ChessCNN(nn.Module):
    def __init__(self, move_output_size):
        super().__init__()
        self.conv1 = nn.Conv2d(15, 32, 3, padding=1)
        self.conv2 = nn.Conv2d(32, 64, 3, padding=1)
        self.conv3 = nn.Conv2d(64, 64, 3, padding=1)
        self.flatten = nn.Flatten()
        self.attn = SelfAttention(64)
        self.fc = nn.Linear(64 * 64, move_output_size)

    def forward(self, x):
        x = F.relu(self.conv1(x))
        x = F.relu(self.conv2(x))
        x = F.relu(self.conv3(x))
        x = x.view(x.size(0), 64, 8*8)
        x = x.transpose(1, 2)
        x = self.attn(x)
        x = x.view(x.size(0), -1)
        return self.fc(x)