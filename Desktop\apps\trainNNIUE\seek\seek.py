import os
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
import chess
import chess.pgn
from tkinter import Tk, filedialog, Button, Label, Entry, StringVar, Frame, messagebox
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
from tqdm import tqdm
import threading
import multiprocessing
from concurrent.futures import ThreadPoolExecutor

# --- Constants ---
BOARD_SIZE = 8
NUM_PIECE_TYPES = 12
REGULAR_MOVES = 64 * 64
PROMOTION_MOVES = 64 * 64 * 4
BATCH_SIZE = 256
EPOCHS = 50
CHECKPOINT_INTERVAL = 5

# --- Chess CNN Model ---
class ResidualBlock(nn.Module):
    def __init__(self, channels):
        super(ResidualBlock, self).__init__()
        self.conv1 = nn.Conv2d(channels, channels, kernel_size=3, padding=1)
        self.bn1 = nn.BatchNorm2d(channels)
        self.conv2 = nn.Conv2d(channels, channels, kernel_size=3, padding=1)
        self.bn2 = nn.BatchNorm2d(channels)

    def forward(self, x):
        residual = x
        x = F.relu(self.bn1(self.conv1(x)))
        x = self.bn2(self.conv2(x))
        x += residual
        return F.relu(x)

class ChessCNN(nn.Module):
    def __init__(self):
        super(ChessCNN, self).__init__()
        self.conv1 = nn.Conv2d(NUM_PIECE_TYPES, 256, kernel_size=3, padding=1)
        self.bn1 = nn.BatchNorm2d(256)

        self.residual_blocks = nn.Sequential(*[ResidualBlock(256) for _ in range(5)])

        self.regular_head = nn.Sequential(
            nn.Conv2d(256, 64, kernel_size=1),
            nn.AdaptiveAvgPool2d((8, 8)),
            nn.Flatten(),
            nn.Linear(64 * 8 * 8, REGULAR_MOVES)
        )
        self.promo_head = nn.Sequential(
            nn.Conv2d(256, 64, kernel_size=1),
            nn.AdaptiveAvgPool2d((8, 8)),
            nn.Flatten(),
            nn.Linear(64 * 8 * 8, PROMOTION_MOVES)
        )

    def forward(self, x):
        x = F.relu(self.bn1(self.conv1(x)))
        x = self.residual_blocks(x)
        return self.regular_head(x), self.promo_head(x)

# --- Dataset with PGN Caching + Multi-threading ---
class ChessDataset(Dataset):
    def __init__(self, pgn_folder):
        self.positions = []
        self.regular_moves = []
        self.promotion_moves = []

        self.cache_path = os.path.join(pgn_folder, "cached_data.npz")
        if os.path.exists(self.cache_path):
            self.load_cache()
        else:
            self.load_pgns_multithreaded(pgn_folder)
            self.save_cache()

    def save_cache(self):
        np.savez_compressed(self.cache_path,
                            positions=np.array(self.positions, dtype=np.uint8),
                            regular_moves=np.array(self.regular_moves, dtype=np.int32),
                            promotion_moves=np.array(self.promotion_moves, dtype=np.int32))

    def load_cache(self):
        data = np.load(self.cache_path)
        self.positions = data["positions"]
        self.regular_moves = data["regular_moves"]
        self.promotion_moves = data["promotion_moves"]

    def load_pgns_multithreaded(self, pgn_folder):
        pgn_files = [os.path.join(pgn_folder, f) for f in os.listdir(pgn_folder) if f.endswith(".pgn")]
        with ThreadPoolExecutor(max_workers=min(8, len(pgn_files))) as executor:
            results = list(tqdm(executor.map(self.parse_single_pgn, pgn_files), desc="Parsing PGNs"))
        for pos_list, reg_list, promo_list in results:
            self.positions.extend(pos_list)
            self.regular_moves.extend(reg_list)
            self.promotion_moves.extend(promo_list)

    def parse_single_pgn(self, path):
        positions, regular, promo = [], [], []
        with open(path, "r") as f:
            while True:
                game = chess.pgn.read_game(f)
                if game is None:
                    break
                board = game.board()
                for move in game.mainline_moves():
                    positions.append(self.board_to_tensor(board))
                    if move.promotion:
                        p = {chess.KNIGHT: 0, chess.BISHOP: 1, chess.ROOK: 2, chess.QUEEN: 3}[move.promotion]
                        promo.append(move.from_square * 64 * 4 + move.to_square * 4 + p)
                        regular.append(-1)
                    else:
                        regular.append(move.from_square * 64 + move.to_square)
                        promo.append(-1)
                    board.push(move)
        return positions, regular, promo

    def board_to_tensor(self, board):
        tensor = np.zeros((NUM_PIECE_TYPES, 8, 8), dtype=np.uint8)
        for square in chess.SQUARES:
            piece = board.piece_at(square)
            if piece:
                idx = piece.piece_type - 1 + (6 if piece.color == chess.BLACK else 0)
                row, col = divmod(square, 8)
                tensor[idx, row, col] = 1
        return tensor

    def __len__(self):
        return len(self.positions)

    def __getitem__(self, idx):
        return (
            torch.tensor(self.positions[idx], dtype=torch.float32),
            torch.tensor(self.regular_moves[idx], dtype=torch.long),
            torch.tensor(self.promotion_moves[idx], dtype=torch.long)
        )

# --- Training Function ---
def train_model(train_folder, val_folder, checkpoint_path, save_dir, gui_updater):
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")

    model = ChessCNN().to(device)
    optimizer = optim.Adam(model.parameters(), lr=0.001)
    criterion = nn.CrossEntropyLoss(ignore_index=-1)

    start_epoch = 0
    best_val_loss = float('inf')
    if checkpoint_path and os.path.exists(checkpoint_path):
        print(f"Loading checkpoint from {checkpoint_path}")
        checkpoint = torch.load(checkpoint_path, map_location=device)
        model.load_state_dict(checkpoint["model_state"])
        optimizer.load_state_dict(checkpoint["optimizer_state"])
        start_epoch = checkpoint["epoch"] + 1
        best_val_loss = checkpoint.get("best_val_loss", float('inf'))

    print("Loading training data...")
    train_dataset = ChessDataset(train_folder)
    print("Loading validation data...")
    val_dataset = ChessDataset(val_folder)
    train_loader = DataLoader(train_dataset, batch_size=BATCH_SIZE, shuffle=True,
                              num_workers=4, pin_memory=True, persistent_workers=True)
    val_loader = DataLoader(val_dataset, batch_size=BATCH_SIZE,
                            num_workers=4, pin_memory=True, persistent_workers=True)

    os.makedirs(save_dir, exist_ok=True)
    train_losses, val_losses = [], []

    for epoch in range(start_epoch, EPOCHS):
        model.train()
        epoch_train_loss = 0.0
        pbar = tqdm(train_loader, desc=f"Epoch {epoch + 1}/{EPOCHS} [Training]")
        for inputs, regular_labels, promo_labels in pbar:
            inputs, regular_labels, promo_labels = inputs.to(device), regular_labels.to(device), promo_labels.to(device)
            optimizer.zero_grad()
            regular_logits, promo_logits = model(inputs)
            loss_regular = criterion(regular_logits, regular_labels)
            loss_promo = criterion(promo_logits, promo_labels)
            loss = (loss_regular if not torch.isnan(loss_regular) else 0) + \
                   (loss_promo if not torch.isnan(loss_promo) else 0)
            if loss != 0:
                loss.backward()
                optimizer.step()
                epoch_train_loss += loss.item()
            pbar.set_postfix(loss=f"{loss.item():.4f}")

        model.eval()
        epoch_val_loss = 0.0
        with torch.no_grad():
            pbar_val = tqdm(val_loader, desc=f"Epoch {epoch + 1}/{EPOCHS} [Validation]")
            for inputs, regular_labels, promo_labels in pbar_val:
                inputs, regular_labels, promo_labels = inputs.to(device), regular_labels.to(device), promo_labels.to(device)
                regular_logits, promo_logits = model(inputs)
                loss_regular = criterion(regular_logits, regular_labels)
                loss_promo = criterion(promo_logits, promo_labels)
                loss = (loss_regular if not torch.isnan(loss_regular) else 0) + \
                       (loss_promo if not torch.isnan(loss_promo) else 0)
                if loss != 0:
                    epoch_val_loss += loss.item()
                pbar_val.set_postfix(loss=f"{loss.item():.4f}")

        avg_train_loss = epoch_train_loss / len(train_loader)
        avg_val_loss = epoch_val_loss / len(val_loader)
        train_losses.append(avg_train_loss)
        val_losses.append(avg_val_loss)

        if gui_updater:
            root.after(0, gui_updater, epoch + 1, avg_train_loss, avg_val_loss, train_losses, val_losses)

        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            torch.save({
                "epoch": epoch,
                "model_state": model.state_dict(),
                "optimizer_state": optimizer.state_dict(),
                "train_loss": avg_train_loss,
                "val_loss": avg_val_loss,
                "best_val_loss": best_val_loss,
            }, os.path.join(save_dir, "best_model.pt"))

        if (epoch + 1) % CHECKPOINT_INTERVAL == 0:
            torch.save({
                "epoch": epoch,
                "model_state": model.state_dict(),
                "optimizer_state": optimizer.state_dict(),
                "train_loss": avg_train_loss,
                "val_loss": avg_val_loss,
            }, os.path.join(save_dir, f"checkpoint_epoch_{epoch + 1}.pt"))

    print("Training finished.")

# --- GUI Class ---
class ChessTrainerGUI:
    def __init__(self, root):
        self.root = root
        root.title("Chess CNN Trainer")
        self.setup_ui()

    def setup_ui(self):
        input_frame = Frame(self.root, padx=10, pady=10)
        input_frame.grid(row=0, column=0, sticky="ew")

        Label(input_frame, text="Training PGN Folder:").grid(row=0, column=0, sticky="w", pady=2)
        self.train_folder_var = StringVar()
        Entry(input_frame, textvariable=self.train_folder_var, width=60).grid(row=0, column=1)
        Button(input_frame, text="Browse", command=self.browse_train_folder).grid(row=0, column=2)

        Label(input_frame, text="Validation PGN Folder:").grid(row=1, column=0, sticky="w", pady=2)
        self.val_folder_var = StringVar()
        Entry(input_frame, textvariable=self.val_folder_var, width=60).grid(row=1, column=1)
        Button(input_frame, text="Browse", command=self.browse_val_folder).grid(row=1, column=2)

        Label(input_frame, text="Checkpoint (Optional):").grid(row=2, column=0, sticky="w", pady=2)
        self.checkpoint_var = StringVar()
        Entry(input_frame, textvariable=self.checkpoint_var, width=60).grid(row=2, column=1)
        Button(input_frame, text="Browse", command=self.browse_checkpoint).grid(row=2, column=2)

        Label(input_frame, text="Save Checkpoints To:").grid(row=3, column=0, sticky="w", pady=2)
        self.save_dir_var = StringVar(value="checkpoints")
        Entry(input_frame, textvariable=self.save_dir_var, width=60).grid(row=3, column=1)
        Button(input_frame, text="Browse", command=self.browse_save_dir).grid(row=3, column=2)

        self.start_button = Button(self.root, text="Start Training", command=self.start_training)
        self.start_button.grid(row=1, column=0, pady=10)

        self.metrics_frame = Frame(self.root)
        self.metrics_frame.grid(row=2, column=0, padx=10, pady=10, sticky="nsew")
        self.fig = Figure(figsize=(8, 5), dpi=100)
        self.ax = self.fig.add_subplot(111)
        self.canvas = FigureCanvasTkAgg(self.fig, master=self.metrics_frame)
        self.canvas.get_tk_widget().pack(side="top", fill="both", expand=True)
        self.draw_initial_plot()

        self.progress_label = Label(self.root, text="Ready to train.")
        self.progress_label.grid(row=3, column=0, sticky="w", padx=10)

    def draw_initial_plot(self):
        self.ax.set_title("Training and Validation Loss")
        self.ax.set_xlabel("Epoch")
        self.ax.set_ylabel("Loss")
        self.ax.grid(True)
        self.ax.legend(["Train Loss", "Val Loss"])
        self.canvas.draw()

    def browse_train_folder(self):
        folder = filedialog.askdirectory(title="Select Training PGN Folder")
        if folder:
            self.train_folder_var.set(folder)

    def browse_val_folder(self):
        folder = filedialog.askdirectory(title="Select Validation PGN Folder")
        if folder:
            self.val_folder_var.set(folder)

    def browse_checkpoint(self):
        file = filedialog.askopenfilename(title="Select Checkpoint File", filetypes=[("PyTorch Checkpoint", "*.pt")])
        if file:
            self.checkpoint_var.set(file)

    def browse_save_dir(self):
        folder = filedialog.askdirectory(title="Select Folder to Save Checkpoints")
        if folder:
            self.save_dir_var.set(folder)

    def update_metrics(self, epoch, train_loss, val_loss, train_losses, val_losses):
        self.ax.clear()
        self.ax.plot(range(1, epoch + 1), train_losses, 'o-', label="Train Loss")
        self.ax.plot(range(1, epoch + 1), val_losses, 'o-', label="Val Loss")
        self.ax.set_title("Training and Validation Loss")
        self.ax.set_xlabel("Epoch")
        self.ax.set_ylabel("Loss")
        self.ax.grid(True)
        self.ax.legend()
        self.canvas.draw()
        self.progress_label.config(text=f"Epoch {epoch} | Train Loss: {train_loss:.4f} | Val Loss: {val_loss:.4f}")
        self.root.update_idletasks()

    def training_complete(self):
        self.progress_label.config(text="Training finished.")
        self.start_button.config(state="normal")

    def start_training(self):
        train_folder = self.train_folder_var.get()
        val_folder = self.val_folder_var.get()
        checkpoint = self.checkpoint_var.get() or None
        save_dir = self.save_dir_var.get()

        if not os.path.isdir(train_folder) or not os.path.isdir(val_folder):
            messagebox.showerror("Error", "Select valid PGN folders.")
            return

        self.start_button.config(state="disabled")
        self.progress_label.config(text="Starting training...")

        def run_training():
            try:
                train_model(train_folder, val_folder, checkpoint, save_dir, self.update_metrics)
            except Exception as e:
                self.root.after(0, messagebox.showerror, "Training Error", str(e))
            finally:
                self.root.after(0, self.training_complete)

        threading.Thread(target=run_training, daemon=True).start()

# --- Main ---
if __name__ == "__main__":
    multiprocessing.freeze_support()
    root = Tk()
    app = ChessTrainerGUI(root)
    root.mainloop()
