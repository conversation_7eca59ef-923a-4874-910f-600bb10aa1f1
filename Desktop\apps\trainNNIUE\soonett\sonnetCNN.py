import sys
import os
import torch
import torch.nn as nn
import torch.nn.functional as F
import chess
import chess.pgn
import numpy as np
import time
import threading
import queue

# --- GUI Libraries ---
import tkinter as tk
from tkinter import ttk, filedialog, messagebox

# --- Matplotlib for plotting ---
from matplotlib.figure import Figure
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg

# --- Your Provided Chess AI Code ---
# (The backend logic remains unchanged)

class ChessPositionEncoder:
    """Encodes chess positions into tensor format"""
    def __init__(self):
        self.piece_to_idx = {
            chess.PAWN: 1, chess.KNIGHT: 2, chess.BISHOP: 3,
            chess.ROOK: 4, chess.QUEEN: 5, chess.KING: 6
        }
        
    def encode_position(self, board: chess.Board) -> torch.Tensor:
        position = torch.zeros(14, 8, 8, dtype=torch.float32)
        for square in chess.SQUARES:
            piece = board.piece_at(square)
            if piece:
                row, col = divmod(square, 8)
                piece_type = self.piece_to_idx[piece.piece_type]
                if piece.color == chess.WHITE:
                    position[piece_type - 1, row, col] = 1.0
                else:
                    position[piece_type + 5, row, col] = 1.0
        
        castling_channel = 12
        if board.has_kingside_castling_rights(chess.WHITE): position[castling_channel, 0, 6:8] = 1.0
        if board.has_queenside_castling_rights(chess.WHITE): position[castling_channel, 0, 0:3] = 1.0
        if board.has_kingside_castling_rights(chess.BLACK): position[castling_channel, 7, 6:8] = 1.0
        if board.has_queenside_castling_rights(chess.BLACK): position[castling_channel, 7, 0:3] = 1.0
            
        if board.ep_square:
            row, col = divmod(board.ep_square, 8)
            position[13, row, col] = 1.0
            
        return position

class CompactSelfAttention(nn.Module):
    """Lightweight self-attention for 8x8 spatial features"""
    def __init__(self, channels: int, num_heads: int = 4):
        super().__init__()
        self.channels = channels
        self.num_heads = num_heads
        self.head_dim = channels // num_heads
        self.qkv = nn.Conv2d(channels, channels * 3, 1, bias=False)
        self.proj = nn.Conv2d(channels, channels, 1)
        self.norm = nn.LayerNorm(channels)
        
    def forward(self, x):
        B, C, H, W = x.shape
        residual = x
        qkv = self.qkv(x).view(B, 3, self.num_heads, self.head_dim, H * W)
        q, k, v = qkv.unbind(1)
        q, k, v = q.transpose(-2, -1), k.transpose(-2, -1), v.transpose(-2, -1)
        attn = torch.matmul(q, k.transpose(-2, -1)) / (self.head_dim ** 0.5)
        attn = F.softmax(attn, dim=-1)
        out = torch.matmul(attn, v)
        out = out.transpose(1, 2).contiguous().view(B, H, W, C).permute(0, 3, 1, 2)
        out = self.proj(out)
        out = (out + residual).permute(0, 2, 3, 1)
        out = self.norm(out).permute(0, 3, 1, 2)
        return out

class ChessNet(nn.Module):
    """Compact CNN with self-attention for chess position evaluation"""
    def __init__(self, channels: int = 128, num_blocks: int = 6):
        super().__init__()
        self.input_conv = nn.Conv2d(14, channels, 3, padding=1)
        self.input_norm = nn.BatchNorm2d(channels)
        self.blocks = nn.ModuleList([
            self._make_block(channels, use_attention=(i % 2 == 1)) for i in range(num_blocks)
        ])
        self.policy_conv = nn.Conv2d(channels, 32, 1)
        self.policy_norm = nn.BatchNorm2d(32)
        self.policy_head = nn.Linear(32 * 64, 4096)
        self.value_conv = nn.Conv2d(channels, 1, 1)
        self.value_norm = nn.BatchNorm2d(1)
        self.value_head = nn.Sequential(nn.Linear(64, 256), nn.ReLU(), nn.Linear(256, 1), nn.Tanh())

    def _make_block(self, channels: int, use_attention: bool = False):
        layers = [
            nn.Conv2d(channels, channels, 3, padding=1), nn.BatchNorm2d(channels), nn.ReLU(inplace=True),
            nn.Conv2d(channels, channels, 3, padding=1), nn.BatchNorm2d(channels)
        ]
        if use_attention:
            layers.append(CompactSelfAttention(channels, num_heads=4))
        return nn.Sequential(*layers)

    def forward(self, x):
        x = F.relu(self.input_norm(self.input_conv(x)))
        for block in self.blocks:
            x = F.relu(block(x) + x)
        
        policy = F.relu(self.policy_norm(self.policy_conv(x))).view(x.size(0), -1)
        policy = F.log_softmax(self.policy_head(policy), dim=1)
        
        value = F.relu(self.value_norm(self.value_conv(x))).view(x.size(0), -1)
        value = self.value_head(value)
        return policy, value

class MoveEncoder:
    """Encodes chess moves to indices for training"""
    def __init__(self):
        self.move_to_idx = {}
        idx = 0
        for from_sq in chess.SQUARES:
            for to_sq in chess.SQUARES:
                if from_sq != to_sq:
                    self.move_to_idx[chess.Move(from_sq, to_sq).uci()] = idx
                    idx += 1
                    for promotion in [chess.QUEEN, chess.ROOK, chess.BISHOP, chess.KNIGHT]:
                        if idx < 4096:
                           self.move_to_idx[chess.Move(from_sq, to_sq, promotion=promotion).uci()] = idx
                           idx += 1
    
    def encode_move(self, move: chess.Move) -> int:
        return self.move_to_idx.get(move.uci(), 0)

class ChessDataset:
    """Dataset for loading PGN games, designed to work with the GUI queue"""
    def __init__(self, pgn_folder: str, msg_queue: queue.Queue):
        self.encoder = ChessPositionEncoder()
        self.move_encoder = MoveEncoder()
        self.examples = []
        self.msg_queue = msg_queue
        self.load_pgn_from_folder(pgn_folder)
    
    def load_pgn_from_folder(self, folder_path: str):
        try:
            pgn_files = [f for f in os.listdir(folder_path) if f.endswith(".pgn")]
            self.msg_queue.put(f"Found {len(pgn_files)} PGN files...")
            
            game_count = 0
            for pgn_file in pgn_files:
                file_path = os.path.join(folder_path, pgn_file)
                with open(file_path, errors='ignore') as f:
                    while True:
                        game = chess.pgn.read_game(f)
                        if game is None: break
                        result = game.headers.get("Result", "*")
                        if result == "*": continue

                        game_value = {"1-0": 1.0, "0-1": -1.0}.get(result, 0.0)
                        board = game.board()
                        for move in game.mainline_moves():
                            position = self.encoder.encode_position(board)
                            move_idx = self.move_encoder.encode_move(move)
                            value = game_value if board.turn == chess.WHITE else -game_value
                            self.examples.append((position, move_idx, value))
                            board.push(move)
                        
                        game_count += 1
                        if game_count % 100 == 0:
                            self.msg_queue.put(f"Processed {game_count} games, loaded {len(self.examples)} positions...")
            
            self.msg_queue.put(f"Finished loading. Loaded {len(self.examples)} examples from {game_count} games.")
        except FileNotFoundError:
             self.msg_queue.put(f"Error: PGN folder not found at {folder_path}")
             self.examples = [] # Ensure dataset is empty
        except Exception as e:
            self.msg_queue.put(f"An error occurred during PGN loading: {e}")
            self.examples = []


    def __len__(self):
        return len(self.examples)
    
    def __getitem__(self, idx):
        return self.examples[idx]

# --- Main Application ---

class ChessNetTrainerApp(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("ChessNet Trainer")
        self.geometry("850x750")

        self.config = {}
        self.train_loss_data = []
        self.val_loss_data = []
        self.training_thread = None
        self.stop_training_flag = threading.Event()
        self.msg_queue = queue.Queue()

        # Create main tabs
        self.notebook = ttk.Notebook(self)
        self.training_tab = ttk.Frame(self.notebook)
        self.configuration_tab = ttk.Frame(self.notebook)
        self.analysis_tab = ttk.Frame(self.notebook)

        self.notebook.add(self.training_tab, text="Training")
        self.notebook.add(self.configuration_tab, text="Configuration")
        self.notebook.add(self.analysis_tab, text="Analysis")
        self.notebook.pack(expand=True, fill="both", padx=10, pady=10)

        # Setup UI for each tab
        self.init_training_ui()
        self.init_configuration_ui()
        self.init_analysis_ui()
        
        self.apply_configuration()
        self.process_queue() # Start listening for messages from the worker thread

    def init_training_ui(self):
        # --- File Selection ---
        file_group = ttk.LabelFrame(self.training_tab, text="File Selection", padding=10)
        file_group.pack(fill="x", padx=5, pady=5)
        
        self.train_pgn_path = tk.StringVar()
        self.val_pgn_path = tk.StringVar()
        
        ttk.Label(file_group, text="Training PGN Folder:").grid(row=0, column=0, sticky="w", padx=5, pady=2)
        ttk.Entry(file_group, textvariable=self.train_pgn_path, width=60).grid(row=0, column=1, sticky="we", padx=5)
        ttk.Button(file_group, text="Browse", command=lambda: self.browse_folder(self.train_pgn_path)).grid(row=0, column=2, padx=5)
        
        ttk.Label(file_group, text="Validation PGN Folder:").grid(row=1, column=0, sticky="w", padx=5, pady=2)
        ttk.Entry(file_group, textvariable=self.val_pgn_path, width=60).grid(row=1, column=1, sticky="we", padx=5)
        ttk.Button(file_group, text="Browse", command=lambda: self.browse_folder(self.val_pgn_path)).grid(row=1, column=2, padx=5)
        file_group.columnconfigure(1, weight=1)

        # --- Controls ---
        controls_group = ttk.LabelFrame(self.training_tab, text="Controls", padding=10)
        controls_group.pack(fill="x", padx=5, pady=5)
        
        self.start_btn = ttk.Button(controls_group, text="Start Training", command=self.start_training)
        self.start_btn.pack(side="left", padx=5)
        self.stop_btn = ttk.Button(controls_group, text="Stop Training", command=self.stop_training, state="disabled")
        self.stop_btn.pack(side="left", padx=5)
        ttk.Button(controls_group, text="Run Validation", state="disabled").pack(side="left", padx=5)
        ttk.Button(controls_group, text="Load Checkpoint", state="disabled").pack(side="left", padx=5)
        ttk.Button(controls_group, text="Save Checkpoint", state="disabled").pack(side="left", padx=5)
        ttk.Button(controls_group, text="Reset Progress", command=self.reset_progress).pack(side="left", padx=5)

        # --- Progress ---
        progress_group = ttk.LabelFrame(self.training_tab, text="Progress", padding=10)
        progress_group.pack(fill="x", padx=5, pady=5)
        self.progress_var = tk.StringVar(value="Ready to train.")
        ttk.Label(progress_group, textvariable=self.progress_var).pack(fill="x", expand=True)
        
        # --- Training Progress Graph ---
        graph_group = ttk.LabelFrame(self.training_tab, text="Training Progress", padding=10)
        graph_group.pack(fill="both", expand=True, padx=5, pady=5)
        
        self.figure = Figure(figsize=(5, 4), dpi=100)
        self.ax = self.figure.add_subplot(111)
        self.ax.set_title("Training and Validation Loss")
        self.ax.set_xlabel("Epoch")
        self.ax.set_ylabel("Loss")
        
        self.train_line, = self.ax.plot([], [], 'o-', label='Train Loss')
        self.val_line, = self.ax.plot([], [], 'o-', label='Validation Loss')
        self.ax.legend()
        self.ax.grid(True)

        self.canvas = FigureCanvasTkAgg(self.figure, master=graph_group)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(side=tk.TOP, fill=tk.BOTH, expand=True)

    def init_configuration_ui(self):
        main_frame = ttk.Frame(self.configuration_tab, padding=10)
        main_frame.pack(fill="both", expand=True)
        
        # --- Training Parameters ---
        params_group = ttk.LabelFrame(main_frame, text="Training Parameters", padding=10)
        params_group.pack(fill="x", pady=5)
        
        self.param_vars = {
            "Epochs": tk.StringVar(value="200"),
            "Batch Size": tk.StringVar(value="256"),
            "Learning Rate": tk.StringVar(value="0.001"),
            "Weight Decay": tk.StringVar(value="1e-06"),
            "Validation Interval": tk.StringVar(value="1"),
            "Checkpoint Interval": tk.StringVar(value="10"),
            "Early Stopping Patience": tk.StringVar(value="200")
        }
        
        for i, (name, var) in enumerate(self.param_vars.items()):
            ttk.Label(params_group, text=f"{name}:").grid(row=i, column=0, sticky="w", padx=5, pady=5)
            ttk.Entry(params_group, textvariable=var, width=20).grid(row=i, column=1, sticky="w", padx=5, pady=5)

        # --- Checkboxes ---
        checkbox_group = ttk.Frame(main_frame, padding=(0, 10))
        checkbox_group.pack(fill="x", pady=5)
        
        self.autosave_best_var = tk.BooleanVar()
        self.autoload_latest_var = tk.BooleanVar()
        self.time_based_var = tk.BooleanVar()
        
        ttk.Checkbutton(checkbox_group, text="Auto-save Best Model", variable=self.autosave_best_var).pack(anchor="w")
        ttk.Checkbutton(checkbox_group, text="Auto-load Latest Checkpoint on Start", variable=self.autoload_latest_var).pack(anchor="w")
        ttk.Checkbutton(checkbox_group, text="Enable Time-Based Checkpoints", variable=self.time_based_var).pack(anchor="w")
        
        # --- Apply Button ---
        apply_btn = ttk.Button(main_frame, text="Apply Configuration", command=self.apply_configuration, width=30)
        apply_btn.pack(pady=20)

    def init_analysis_ui(self):
        ttk.Label(self.analysis_tab, text="Analysis tools and visualizations will be available here.", padding=20).pack()

    def browse_folder(self, path_var):
        folder = filedialog.askdirectory(title="Select Folder")
        if folder:
            path_var.set(folder)
            
    def apply_configuration(self):
        try:
            self.config = {
                'epochs': int(self.param_vars['Epochs'].get()),
                'batch_size': int(self.param_vars['Batch Size'].get()),
                'learning_rate': float(self.param_vars['Learning Rate'].get()),
                'weight_decay': float(self.param_vars['Weight Decay'].get()),
                'train_pgn_folder': self.train_pgn_path.get(),
                'val_pgn_folder': self.val_pgn_path.get()
            }
            # No message box on silent apply, only on button press
        except (ValueError, tk.TclError) as e: # <--- *** THIS IS THE FIX ***
            messagebox.showerror("Error", f"Invalid input for configuration: {e}", parent=self)

    def start_training(self):
        if not self.train_pgn_path.get() or not self.val_pgn_path.get():
            messagebox.showwarning("Missing Path", "Please provide paths for both training and validation PGN folders.", parent=self)
            return

        self.apply_configuration()
        self.start_btn.config(state="disabled")
        self.stop_btn.config(state="normal")
        self.reset_progress()
        self.stop_training_flag.clear()
        
        self.training_thread = threading.Thread(
            target=training_worker_function,
            args=(self.config, self.msg_queue, self.stop_training_flag),
            daemon=True
        )
        self.training_thread.start()

    def stop_training(self):
        if self.training_thread and self.training_thread.is_alive():
            self.msg_queue.put("STOP_REQUESTED")
            self.stop_training_flag.set()
            self.progress_var.set("Stopping training...")
            self.stop_btn.config(state="disabled")

    def reset_progress(self):
        self.train_loss_data.clear()
        self.val_loss_data.clear()
        self.train_line.set_data([], [])
        self.val_line.set_data([], [])
        self.ax.relim()
        self.ax.autoscale_view()
        self.canvas.draw()
        self.progress_var.set("Ready.")

    def update_plot(self):
        epochs = range(1, len(self.train_loss_data) + 1)
        self.train_line.set_data(epochs, self.train_loss_data)
        self.val_line.set_data(epochs, self.val_loss_data)
        
        self.ax.relim()
        self.ax.autoscale_view()
        self.figure.tight_layout()
        self.canvas.draw()
    
    def process_queue(self):
        try:
            message = self.msg_queue.get_nowait()
            if isinstance(message, str):
                self.progress_var.set(message)
                if "Training finished" in message or "Error" in message or "terminated" in message:
                    self.start_btn.config(state="normal")
                    self.stop_btn.config(state="disabled")

            elif isinstance(message, dict) and 'epoch' in message:
                epoch_data = message
                self.progress_var.set(
                    f"Epoch {epoch_data['epoch']}/{self.config['epochs']}, "
                    f"Train Loss: {epoch_data['train_loss']:.4f}, "
                    f"Val Loss: {epoch_data['val_loss']:.4f}"
                )
                self.train_loss_data.append(epoch_data['train_loss'])
                self.val_loss_data.append(epoch_data['val_loss'])
                self.update_plot()
                
        except queue.Empty:
            pass
        finally:
            self.after(100, self.process_queue)


def training_worker_function(config, msg_queue, stop_flag):
    """The function that runs in a separate thread to handle the training"""
    try:
        msg_queue.put("Initializing training...")
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        msg_queue.put(f"Using device: {device}")

        model = ChessNet(channels=96, num_blocks=4).to(device)
        
        msg_queue.put("Loading training dataset...")
        train_dataset = ChessDataset(config['train_pgn_folder'], msg_queue)
        if not train_dataset.examples or stop_flag.is_set():
            msg_queue.put("Training stopped due to data loading issues or user request."); return
        
        msg_queue.put("Loading validation dataset...")
        val_dataset = ChessDataset(config['val_pgn_folder'], msg_queue)
        if not val_dataset.examples or stop_flag.is_set():
            msg_queue.put("Training stopped due to data loading issues or user request."); return

        train_loader = torch.utils.data.DataLoader(train_dataset, batch_size=config['batch_size'], shuffle=True)
        val_loader = torch.utils.data.DataLoader(val_dataset, batch_size=config['batch_size'])

        optimizer = torch.optim.Adam(model.parameters(), lr=config['learning_rate'], weight_decay=config['weight_decay'])
        policy_criterion = nn.NLLLoss()
        value_criterion = nn.MSELoss()
        
        msg_queue.put("Starting training loop...")
        for epoch in range(config['epochs']):
            if stop_flag.is_set():
                msg_queue.put("Training stopped by user."); break
            
            # Training phase
            model.train()
            total_train_loss = 0
            for batch in train_loader:
                if stop_flag.is_set(): break
                positions, moves, values = [b.to(device) for b in batch]
                optimizer.zero_grad()
                policy_logits, value_pred = model(positions)
                loss = policy_criterion(policy_logits, moves) + value_criterion(value_pred.squeeze(), values)
                loss.backward()
                optimizer.step()
                total_train_loss += loss.item()
            if stop_flag.is_set(): msg_queue.put("Training stopped by user."); break
            avg_train_loss = total_train_loss / len(train_loader)

            # Validation phase
            model.eval()
            total_val_loss = 0
            with torch.no_grad():
                for batch in val_loader:
                    if stop_flag.is_set(): break
                    positions, moves, values = [b.to(device) for b in batch]
                    policy_logits, value_pred = model(positions) # This line had a bug in a previous version, fixed.
                    loss = policy_criterion(policy_logits, moves) + value_criterion(value_pred.squeeze(), values)
                    total_val_loss += loss.item()
            if stop_flag.is_set(): msg_queue.put("Training stopped by user."); break
            avg_val_loss = total_val_loss / len(val_loader) if len(val_loader) > 0 else 0.0

            # Send epoch results back to GUI thread
            msg_queue.put({
                'epoch': epoch + 1,
                'train_loss': avg_train_loss,
                'val_loss': avg_val_loss
            })
        else: # This else belongs to the for loop, runs if the loop completes without break
             msg_queue.put("Training finished successfully.")

    except Exception as e:
        import traceback
        msg_queue.put(f"Error in training thread: {e}\n{traceback.format_exc()}")
    finally:
        # Final signal to ensure GUI buttons are reset
        msg_queue.put("Training thread terminated.")

if __name__ == "__main__":
    app = ChessNetTrainerApp()
    app.mainloop()