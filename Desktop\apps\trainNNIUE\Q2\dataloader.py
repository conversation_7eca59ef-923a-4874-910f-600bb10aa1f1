# dataloader.py - Super Fast PGN Loading
import chess.pgn
import numpy as np
import torch
from tqdm import tqdm
from torch.utils.data import Dataset
import multiprocessing as mp
import os
import pickle
import hashlib
from concurrent.futures import ProcessPoolExecutor, as_completed
import mmap
from utils import board_to_planes_fast, get_move_legality_mask, MOVE_TO_IDX

# Global function for multiprocessing (must be at module level)
def process_pgn_chunk(chunk_info, augment=True):
    """Process a chunk of PGN file in parallel"""
    positions = []
    moves = []
    results = []
    masks = []

    file_path = chunk_info['file_path']
    start = chunk_info['start']
    end = chunk_info['end']
    max_games = chunk_info.get('max_games')

    try:
        with open(file_path, 'rb') as f:
            f.seek(start)
            content = f.read(end - start).decode('utf-8', errors='ignore')

        # Parse games from chunk content
        import io
        game_count = 0
        content_io = io.StringIO(content)

        while True:
            try:
                game = chess.pgn.read_game(content_io)
                if game is None:
                    break
                if max_games and game_count >= max_games:
                    break

                board = game.board()
                result_str = game.headers.get("Result", "½-½")
                result = {'1-0': 1.0, '0-1': 0.0, '1/2-1/2': 0.5}.get(result_str, 0.5)

                for move in game.mainline_moves():
                    uci = move.uci()
                    if uci not in MOVE_TO_IDX:
                        board.push(move)
                        continue

                    # Process position (with optional augmentation)
                    flips = [False]
                    if augment and np.random.rand() < 0.5:
                        flips.append(True)

                    for flip in flips:
                        try:
                            aug_board = board.copy() if not flip else mirror_board_fast(board)
                            plane = board_to_planes_fast(aug_board, flip=flip)
                            move_idx = get_move_index_fast(uci, flip)
                            if move_idx is None:
                                continue

                            mask = get_move_legality_mask(aug_board)

                            positions.append(plane)
                            moves.append(move_idx)
                            results.append(result)
                            masks.append(mask)
                        except Exception:
                            continue

                    board.push(move)

                game_count += 1

            except Exception:
                continue

    except Exception as e:
        print(f"Error processing chunk: {e}")

    return positions, moves, results, masks

def mirror_board_fast(board):
    """Fast board mirroring for augmentation"""
    flipped = chess.Board.empty()
    for sq in chess.SQUARES:
        piece = board.piece_at(sq)
        if piece:
            new_sq = chess.square(7 - (sq % 8), 7 - (sq // 8))
            flipped.set_piece_at(new_sq, piece)
    flipped.turn = not board.turn
    flipped.castling_rights = 0
    # Simplified castling rights handling for speed
    return flipped

def get_move_index_fast(uci, flipped):
    """Fast move index lookup with caching"""
    if not flipped:
        return MOVE_TO_IDX.get(uci)

    # Fast flip calculation
    try:
        from_file, from_rank = ord(uci[0]) - ord('a'), int(uci[1]) - 1
        to_file, to_rank = ord(uci[2]) - ord('a'), int(uci[3]) - 1

        new_from_file = 7 - from_file
        new_from_rank = 7 - from_rank
        new_to_file = 7 - to_file
        new_to_rank = 7 - to_rank

        new_uci = f"{chr(new_from_file + ord('a'))}{new_from_rank + 1}{chr(new_to_file + ord('a'))}{new_to_rank + 1}"
        if len(uci) > 4:  # promotion
            new_uci += uci[4:]

        return MOVE_TO_IDX.get(new_uci)
    except:
        return None

class ChessDataset(Dataset):
    def __init__(self, pgn_path, max_games=None, augment=True, use_cache=True, num_workers=None):
        self.positions = []
        self.moves = []
        self.results = []
        self.masks = []  # legality masks
        self.augment = augment
        self.use_cache = use_cache
        self.num_workers = num_workers or min(8, os.cpu_count())

        # Cache directory
        self.cache_dir = "pgn_cache"
        if not os.path.exists(self.cache_dir):
            os.makedirs(self.cache_dir)

        self.load_pgn_fast(pgn_path, max_games)

    def get_cache_key(self, pgn_path, max_games, augment):
        """Generate a unique cache key for this dataset configuration"""
        # Get file stats for cache invalidation
        stat = os.stat(pgn_path)
        file_info = f"{pgn_path}_{stat.st_size}_{stat.st_mtime}_{max_games}_{augment}"
        return hashlib.md5(file_info.encode()).hexdigest()

    def load_from_cache(self, cache_key):
        """Load dataset from cache if available"""
        cache_file = os.path.join(self.cache_dir, f"{cache_key}.pkl")
        if os.path.exists(cache_file):
            try:
                with open(cache_file, 'rb') as f:
                    data = pickle.load(f)
                    self.positions = data['positions']
                    self.moves = data['moves']
                    self.results = data['results']
                    self.masks = data['masks']
                    return True
            except Exception as e:
                print(f"Cache load failed: {e}")
                return False
        return False

    def save_to_cache(self, cache_key):
        """Save dataset to cache"""
        cache_file = os.path.join(self.cache_dir, f"{cache_key}.pkl")
        try:
            data = {
                'positions': self.positions,
                'moves': self.moves,
                'results': self.results,
                'masks': self.masks
            }
            with open(cache_file, 'wb') as f:
                pickle.dump(data, f, protocol=pickle.HIGHEST_PROTOCOL)
        except Exception as e:
            print(f"Cache save failed: {e}")

    def load_pgn_fast(self, pgn_path, max_games):
        """Super fast PGN loading with caching and parallel processing"""
        # Check cache first
        cache_key = self.get_cache_key(pgn_path, max_games, self.augment)
        if self.use_cache and self.load_from_cache(cache_key):
            print(f"Loaded from cache: {len(self.positions)} positions")
            return

        print("Loading PGN with optimizations...")

        # Use memory-mapped file for faster I/O
        file_size = os.path.getsize(pgn_path)

        # Split file into chunks for parallel processing
        chunk_size = max(file_size // self.num_workers, 1024 * 1024)  # At least 1MB per chunk
        chunks = self.split_pgn_file(pgn_path, chunk_size, max_games)

        # Process chunks in parallel
        all_positions = []
        all_moves = []
        all_results = []
        all_masks = []

        with ProcessPoolExecutor(max_workers=self.num_workers) as executor:
            futures = []
            for chunk_info in chunks:
                future = executor.submit(process_pgn_chunk, chunk_info, self.augment)
                futures.append(future)

            # Collect results with progress bar
            with tqdm(total=len(futures), desc="Processing chunks") as pbar:
                for future in as_completed(futures):
                    try:
                        positions, moves, results, masks = future.result()
                        all_positions.extend(positions)
                        all_moves.extend(moves)
                        all_results.extend(results)
                        all_masks.extend(masks)
                        pbar.update(1)
                    except Exception as e:
                        print(f"Chunk processing failed: {e}")
                        pbar.update(1)

        # Store results
        self.positions = all_positions
        self.moves = all_moves
        self.results = all_results
        self.masks = all_masks

        print(f"Loaded {len(self.positions)} positions from PGN")

        # Save to cache
        if self.use_cache:
            self.save_to_cache(cache_key)

    def split_pgn_file(self, pgn_path, chunk_size, max_games):
        """Split PGN file into chunks for parallel processing"""
        chunks = []

        with open(pgn_path, 'rb') as f:
            chunk_start = 0
            chunk_id = 0
            games_per_chunk = max_games // self.num_workers if max_games else None

            while chunk_start < os.path.getsize(pgn_path):
                chunk_end = min(chunk_start + chunk_size, os.path.getsize(pgn_path))

                # Adjust chunk end to complete game boundary
                if chunk_end < os.path.getsize(pgn_path):
                    f.seek(chunk_end)
                    # Find next game start
                    while chunk_end < os.path.getsize(pgn_path):
                        line = f.readline()
                        if line.startswith(b'[Event ') or not line:
                            break
                        chunk_end = f.tell()

                chunks.append({
                    'file_path': pgn_path,
                    'start': chunk_start,
                    'end': chunk_end,
                    'chunk_id': chunk_id,
                    'max_games': games_per_chunk
                })

                chunk_start = chunk_end
                chunk_id += 1

                if max_games and chunk_id >= self.num_workers:
                    break

        return chunks

    def mirror_board(self, board):
        """Return a flipped version of the board for augmentation."""
        flipped = chess.Board.empty()
        for sq in chess.SQUARES:
            piece = board.piece_at(sq)
            if piece:
                new_sq = chess.square(7 - (sq % 8), 7 - (sq // 8))
                flipped.set_piece_at(new_sq, piece)
        flipped.turn = not board.turn
        flipped.castling_rights = 0
        if board.has_kingside_castling_rights(chess.WHITE):
            flipped.set_castling_fen('k')
        if board.has_queenside_castling_rights(chess.WHITE):
            flipped.set_castling_fen('q')
        if board.has_kingside_castling_rights(chess.BLACK):
            flipped.set_castling_fen('K')
        if board.has_queenside_castling_rights(chess.BLACK):
            flipped.set_castling_fen('Q')
        return flipped

    def load_pgn(self, filepath, max_games):
        """Fallback optimized single-threaded PGN loading"""
        print("Using optimized single-threaded loading...")

        # Pre-allocate lists with estimated size for better memory performance
        estimated_positions = (max_games or 1000) * 40  # ~40 positions per game average

        with open(filepath, 'rb') as f:
            # Use memory mapping for large files
            if os.path.getsize(filepath) > 100 * 1024 * 1024:  # 100MB+
                with mmap.mmap(f.fileno(), 0, access=mmap.ACCESS_READ) as mmapped_file:
                    content = mmapped_file.read().decode('utf-8', errors='ignore')
            else:
                content = f.read().decode('utf-8', errors='ignore')

        # Parse all games at once
        import io
        content_io = io.StringIO(content)
        game_count = 0

        with tqdm(desc="Loading PGN (optimized)") as pbar:
            while True:
                try:
                    game = chess.pgn.read_game(content_io)
                    if game is None:
                        break
                    if max_games and game_count >= max_games:
                        break

                    board = game.board()
                    result_str = game.headers.get("Result", "½-½")
                    result = {'1-0': 1.0, '0-1': 0.0, '1/2-1/2': 0.5}.get(result_str, 0.5)

                    # Batch process moves for better performance
                    move_batch = []
                    board_states = []

                    for move in game.mainline_moves():
                        uci = move.uci()
                        if uci not in MOVE_TO_IDX:
                            board.push(move)
                            continue

                        move_batch.append((board.copy(), uci, result))
                        board.push(move)

                    # Process batch
                    for board_state, uci, game_result in move_batch:
                        # Augment: random flip
                        flips = [False]
                        if self.augment and np.random.rand() < 0.5:
                            flips.append(True)

                        for flip in flips:
                            try:
                                aug_board = board_state if not flip else self.mirror_board(board_state)
                                plane = board_to_planes_fast(aug_board, flip=flip)
                                move_idx = get_move_index_fast(uci, flip)
                                if move_idx is None:
                                    continue

                                mask = get_move_legality_mask(aug_board)

                                self.positions.append(plane)
                                self.moves.append(move_idx)
                                self.results.append(game_result)
                                self.masks.append(mask)
                            except Exception:
                                continue

                    game_count += 1
                    if game_count % 100 == 0:  # Update progress every 100 games
                        pbar.update(100)

                except Exception as e:
                    print(f"Error processing game {game_count}: {e}")
                    continue

        print(f"Loaded {len(self.positions)} positions from {game_count} games")

    def __len__(self):
        return len(self.positions)

    def __getitem__(self, idx):
        return (
            self.positions[idx],
            self.moves[idx],
            self.results[idx],
            self.masks[idx]
        )

# Legacy function - now redirects to fast version
def get_move_index(uci, flipped):
    """Get move index, adjusting for flipped board if needed."""
    return get_move_index_fast(uci, flipped)

def collate_fn(batch):
    """Optimized collate function with pre-allocated tensors"""
    states, moves, results, masks = zip(*batch)

    # Use numpy arrays for faster tensor creation
    states_array = np.array(states, dtype=np.float32)
    moves_array = np.array(moves, dtype=np.int64)
    results_array = np.array(results, dtype=np.float32)
    masks_array = np.array(masks, dtype=np.float32)

    return (
        torch.from_numpy(states_array),
        torch.from_numpy(moves_array),
        torch.from_numpy(results_array),
        torch.from_numpy(masks_array)
    )

# Additional optimization: Pre-compile move mappings for faster lookup
def precompile_move_mappings():
    """Pre-compile move mappings for faster access"""
    global _MOVE_CACHE
    _MOVE_CACHE = {}

    # Pre-compute flipped moves for common moves
    for uci, idx in MOVE_TO_IDX.items():
        try:
            flipped_uci = get_move_index_fast(uci, True)
            if flipped_uci is not None:
                _MOVE_CACHE[f"{uci}_flip"] = flipped_uci
        except:
            continue

# Initialize move cache
_MOVE_CACHE = {}
try:
    precompile_move_mappings()
except:
    pass  # Fallback if utils not available yet