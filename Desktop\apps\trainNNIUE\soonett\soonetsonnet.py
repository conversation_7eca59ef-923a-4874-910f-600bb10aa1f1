import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import torch.nn.functional as F
import chess
import chess.pgn
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import threading
import os
import json
from datetime import datetime
import io
import sys # <-- ADDED FOR TERMINAL PROGRESS

class ChessDataset(Dataset):
    def __init__(self, board_states, legal_masks, move_vectors):
        self.board_states = torch.FloatTensor(board_states)
        self.legal_masks = torch.FloatTensor(legal_masks)
        self.move_vectors = torch.FloatTensor(move_vectors)

    def __len__(self):
        return len(self.board_states)

    def __getitem__(self, idx):
        return {
            'board': self.board_states[idx],
            'mask': self.legal_masks[idx],
            'target': self.move_vectors[idx]
        }

class ChessMoveCNN(nn.Module):
    def __init__(self, num_moves):
        super(ChessMoveCNN, self).__init__()

        # CNN layers for board analysis
        self.conv1 = nn.Conv2d(12, 64, kernel_size=3, padding=1)
        self.bn1 = nn.BatchNorm2d(64)
        self.dropout1 = nn.Dropout2d(0.1)

        self.conv2 = nn.Conv2d(64, 128, kernel_size=3, padding=1)
        self.bn2 = nn.BatchNorm2d(128)
        self.dropout2 = nn.Dropout2d(0.1)

        self.conv3 = nn.Conv2d(128, 256, kernel_size=3, padding=1)
        self.bn3 = nn.BatchNorm2d(256)
        self.dropout3 = nn.Dropout2d(0.2)

        self.conv4 = nn.Conv2d(256, 256, kernel_size=3, padding=1)
        self.bn4 = nn.BatchNorm2d(256)
        self.dropout4 = nn.Dropout2d(0.2)

        # Global average pooling
        self.global_pool = nn.AdaptiveAvgPool2d(1)

        # Dense layers
        self.fc1 = nn.Linear(256, 512)
        self.dropout5 = nn.Dropout(0.3)
        self.fc2 = nn.Linear(512, 256)
        self.dropout6 = nn.Dropout(0.3)

        # Move prediction head
        self.move_head = nn.Linear(256, num_moves)

    def forward(self, board, mask):
        # CNN feature extraction
        x = F.relu(self.bn1(self.conv1(board)))
        x = self.dropout1(x)

        x = F.relu(self.bn2(self.conv2(x)))
        x = self.dropout2(x)

        x = F.relu(self.bn3(self.conv3(x)))
        x = self.dropout3(x)

        x = F.relu(self.bn4(self.conv4(x)))
        x = self.dropout4(x)

        # Global pooling and dense layers
        x = self.global_pool(x)
        x = x.view(x.size(0), -1)  # Flatten

        x = F.relu(self.fc1(x))
        x = self.dropout5(x)
        x = F.relu(self.fc2(x))
        x = self.dropout6(x)

        # Move prediction
        move_logits = self.move_head(x)

        # Apply legal move mask (set illegal moves to very negative values)
        masked_logits = move_logits + (mask - 1.0) * 1e9

        # Softmax over legal moves only
        move_probs = F.softmax(masked_logits, dim=1)

        return move_probs

class ChessMovePredictor:
    def __init__(self):
        # Board representation: 12x8x8 (PyTorch format: C, H, W)
        self.input_shape = (12, 8, 8)
        self.model = None
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"Using device: {self.device}")

        # Move encoding - all possible from-to combinations
        self.move_to_index = {}
        self.index_to_move = {}
        self._build_move_mappings()

        # Training history
        self.train_losses = []
        self.val_losses = []

    def _build_move_mappings(self):
        """Build mappings between moves and indices for all possible moves"""
        idx = 0
        # All possible from-square to to-square combinations
        for from_sq in range(64):
            for to_sq in range(64):
                if from_sq != to_sq:
                    move_key = f"{from_sq}-{to_sq}"
                    self.move_to_index[move_key] = idx
                    self.index_to_move[idx] = move_key
                    idx += 1

        print(f"Total move vocabulary: {len(self.move_to_index)} moves")

    def board_to_tensor(self, board):
        """Convert chess board to 12x8x8 tensor (PyTorch format: C, H, W)"""
        tensor = np.zeros((12, 8, 8), dtype=np.float32)

        piece_map = {
            chess.PAWN: 0, chess.ROOK: 1, chess.KNIGHT: 2,
            chess.BISHOP: 3, chess.QUEEN: 4, chess.KING: 5
        }

        for square in chess.SQUARES:
            piece = board.piece_at(square)
            if piece:
                row = 7 - (square // 8)
                col = square % 8
                piece_idx = piece_map[piece.piece_type]
                color_offset = 0 if piece.color == chess.WHITE else 6
                tensor[piece_idx + color_offset, row, col] = 1.0

        return tensor

    def get_legal_move_mask(self, board):
        """Create mask for legal moves (1 for legal, 0 for illegal)"""
        mask = np.zeros(len(self.move_to_index), dtype=np.float32)

        legal_moves = list(board.legal_moves)
        for move in legal_moves:
            # Handle promotions by using queen promotion as default
            if move.promotion and move.promotion != chess.QUEEN:
                continue

            move_key = f"{move.from_square}-{move.to_square}"
            if move_key in self.move_to_index:
                idx = self.move_to_index[move_key]
                mask[idx] = 1.0

        return mask

    def move_to_vector_masked(self, move, board):
        """Convert move to one-hot vector with illegal move masking"""
        # Get legal move mask
        legal_mask = self.get_legal_move_mask(board)

        # Create target vector
        move_key = f"{move.from_square}-{move.to_square}"
        if move_key in self.move_to_index:
            vector = np.zeros(len(self.move_to_index), dtype=np.float32)
            vector[self.move_to_index[move_key]] = 1.0
            return vector, legal_mask

        return None, None

    def create_model(self):
        """Create CNN model for move prediction with legal move masking"""
        model = ChessMoveCNN(len(self.move_to_index))
        return model.to(self.device)

    def parse_pgn_data(self, pgn_folder, max_games=10000, progress_callback=None):
        """Parse PGN files and extract training data with legal move masking"""
        board_states = []
        move_vectors = []
        legal_masks = []
        games_processed = 0

        pgn_files = [f for f in os.listdir(pgn_folder) if f.endswith('.pgn')]

        for pgn_file in pgn_files:
            if games_processed >= max_games:
                break

            file_path = os.path.join(pgn_folder, pgn_file)

            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    while games_processed < max_games:
                        game = chess.pgn.read_game(f)
                        if game is None:
                            break

                        # Filter by ELO if available
                        try:
                            white_elo = int(game.headers.get("WhiteElo", "0"))
                            black_elo = int(game.headers.get("BlackElo", "0"))
                            if white_elo < 1800 or black_elo < 1800:
                                continue
                        except:
                            pass

                        board = game.board()
                        move_count = 0

                        for move in game.mainline_moves():
                            if move_count > 60:  # Limit moves per game
                                break

                            # Get board state
                            board_tensor = self.board_to_tensor(board)

                            # Get move vector and legal mask
                            move_vector, legal_mask = self.move_to_vector_masked(move, board)

                            if move_vector is not None and legal_mask is not None:
                                board_states.append(board_tensor)
                                move_vectors.append(move_vector)
                                legal_masks.append(legal_mask)

                            board.push(move)
                            move_count += 1

                        games_processed += 1

                        if progress_callback and games_processed % 100 == 0:
                            progress_callback(games_processed, max_games)

            except Exception as e:
                print(f"Error processing {pgn_file}: {e}")
                continue

        return (np.array(board_states), np.array(legal_masks)), np.array(move_vectors)

    # --- MODIFIED FUNCTION ---
    def train_model(self, train_data, val_data=None, epochs=200, batch_size=256,
                   learning_rate=0.001, weight_decay=1e-6, progress_callback=None,
                   auto_save_best=True, checkpoint_interval=10, checkpoint_dir="checkpoints"):
        """Train the model using PyTorch with automatic checkpointing and terminal progress."""

        # Create checkpoint directory
        os.makedirs(checkpoint_dir, exist_ok=True)

        # Create datasets
        (X_train, masks_train), y_train = train_data
        train_dataset = ChessDataset(X_train, masks_train, y_train)
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)

        val_loader = None
        if val_data is not None and len(val_data[0][0]) > 0:
            (X_val, masks_val), y_val = val_data
            val_dataset = ChessDataset(X_val, masks_val, y_val)
            val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
        else:
            val_data = None # Ensure val_data is None if it's empty

        # Create model and optimizer
        if self.model is None:
            self.model = self.create_model()

        optimizer = optim.AdamW(self.model.parameters(), lr=learning_rate, weight_decay=weight_decay)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.5, patience=10)

        criterion = nn.CrossEntropyLoss()

        # Training loop
        self.train_losses = []
        self.val_losses = []
        best_val_loss = float('inf')
        best_train_loss = float('inf')

        for epoch in range(epochs):
            # Training phase
            self.model.train()
            train_loss = 0.0
            train_correct = 0
            train_total = 0

            for batch in train_loader:
                board = batch['board'].to(self.device)
                mask = batch['mask'].to(self.device)
                target = batch['target'].to(self.device)

                optimizer.zero_grad()
                output = self.model(board, mask)
                loss = criterion(output, target)
                loss.backward()
                optimizer.step()

                train_loss += loss.item()

                _, predicted = torch.max(output.data, 1)
                _, targets = torch.max(target.data, 1)
                train_total += target.size(0)
                train_correct += (predicted == targets).sum().item()

            avg_train_loss = train_loss / len(train_loader)
            train_accuracy = 100 * train_correct / train_total
            self.train_losses.append(avg_train_loss)

            # Validation phase
            val_loss = 0.0
            val_accuracy = 0.0
            if val_loader is not None:
                self.model.eval()
                val_correct = 0
                val_total = 0
                with torch.no_grad():
                    for batch in val_loader:
                        board = batch['board'].to(self.device)
                        mask = batch['mask'].to(self.device)
                        target = batch['target'].to(self.device)

                        output = self.model(board, mask)
                        loss = criterion(output, target)
                        val_loss += loss.item()

                        _, predicted = torch.max(output.data, 1)
                        _, targets = torch.max(target.data, 1)
                        val_total += target.size(0)
                        val_correct += (predicted == targets).sum().item()

                val_loss = val_loss / len(val_loader)
                val_accuracy = 100 * val_correct / val_total
                self.val_losses.append(val_loss)
                scheduler.step(val_loss)

            # --- REAL-TIME TERMINAL PROGRESS ---
            progress_str = f"Epoch {epoch + 1}/{epochs} | Train Loss: {avg_train_loss:.4f}, Train Acc: {train_accuracy:.2f}%"
            if val_loader is not None:
                progress_str += f" | Val Loss: {val_loss:.4f}, Val Acc: {val_accuracy:.2f}%"
            
            # Use carriage return to print on the same line
            sys.stdout.write('\r' + progress_str.ljust(100))
            sys.stdout.flush()
            # --- END OF REAL-TIME TERMINAL PROGRESS ---

            # Auto-save best model
            current_loss = val_loss if val_loader is not None else avg_train_loss
            if auto_save_best and current_loss < (best_val_loss if val_loader is not None else best_train_loss):
                if val_loader is not None:
                    best_val_loss = current_loss
                else:
                    best_train_loss = current_loss

                best_model_path = os.path.join(checkpoint_dir, "best_model.pth")
                self.save_model(best_model_path)
                # Note: Commented out print to keep terminal clean
                # print(f"\nSaved best model at epoch {epoch + 1} with loss {current_loss:.4f}")

            # Periodic checkpoint saving
            if checkpoint_interval > 0 and (epoch + 1) % checkpoint_interval == 0:
                checkpoint_path = os.path.join(checkpoint_dir, f"checkpoint_epoch_{epoch + 1}.pth")
                self.save_model(checkpoint_path)
                # Note: Commented out print to keep terminal clean
                # print(f"\nSaved checkpoint at epoch {epoch + 1}")

            # Progress callback for GUI
            if progress_callback:
                progress_info = {
                    'epoch': epoch + 1,
                    'total_epochs': epochs,
                    'train_loss': avg_train_loss,
                    'train_accuracy': train_accuracy,
                    'val_loss': val_loss,
                    'val_accuracy': val_accuracy
                }
                progress_callback(progress_info)

        print() # Add a newline after training is complete
        
        # Save final model
        final_model_path = os.path.join(checkpoint_dir, "final_model.pth")
        self.save_model(final_model_path)
        print(f"Training completed. Final model saved to {final_model_path}")


    def predict_move(self, board):
        """Predict best move for given position"""
        if self.model is None:
            return None

        self.model.eval()
        with torch.no_grad():
            board_tensor = self.board_to_tensor(board)
            board_tensor = torch.FloatTensor(board_tensor).unsqueeze(0).to(self.device)

            legal_mask = self.get_legal_move_mask(board)
            legal_mask = torch.FloatTensor(legal_mask).unsqueeze(0).to(self.device)

            predictions = self.model(board_tensor, legal_mask)[0].cpu().numpy()

        # Get legal moves
        legal_moves = list(board.legal_moves)
        if not legal_moves:
            return None

        # Find best legal move
        best_move = None
        best_score = -1

        for move in legal_moves:
            move_key = f"{move.from_square}-{move.to_square}"
            if move_key in self.move_to_index:
                idx = self.move_to_index[move_key]
                score = predictions[idx]
                if score > best_score:
                    best_score = score
                    best_move = move

        return best_move if best_move else legal_moves[0]

    def save_model(self, filepath):
        """Save the model state"""
        if self.model is not None:
            torch.save({
                'model_state_dict': self.model.state_dict(),
                'move_mappings': {
                    'move_to_index': self.move_to_index,
                    'index_to_move': self.index_to_move
                },
                'train_losses': self.train_losses,
                'val_losses': self.val_losses
            }, filepath)

    def load_model(self, filepath):
        """Load the model state"""
        checkpoint = torch.load(filepath, map_location=self.device)

        # Load move mappings
        self.move_to_index = checkpoint['move_mappings']['move_to_index']
        self.index_to_move = checkpoint['move_mappings']['index_to_move']

        # Create and load model
        self.model = self.create_model()
        self.model.load_state_dict(checkpoint['model_state_dict'])

        # Load training history
        self.train_losses = checkpoint.get('train_losses', [])
        self.val_losses = checkpoint.get('val_losses', [])

class ChessTrainerGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("PyTorch ChessNet Trainer")
        self.root.geometry("800x700")

        self.chess_model = ChessMovePredictor()
        self.training_thread = None
        self.is_training = False

        # Training parameters
        self.epochs = tk.IntVar(value=200)
        self.batch_size = tk.IntVar(value=256)
        self.learning_rate = tk.DoubleVar(value=0.001)
        self.weight_decay = tk.DoubleVar(value=1e-6)
        self.validation_interval = tk.IntVar(value=10)
        self.checkpoint_interval = tk.IntVar(value=10)
        self.early_stopping_patience = tk.IntVar(value=200)
        
        self.auto_save_best = tk.BooleanVar(value=True)
        self.auto_load_checkpoint = tk.BooleanVar(value=True)

        # File paths
        self.training_folder = tk.StringVar()
        self.validation_folder = tk.StringVar()
        self.checkpoint_dir = tk.StringVar(value="checkpoints")

        self.create_widgets()

        # Auto-load checkpoint if enabled
        self.root.after(100, self.auto_load_on_startup)

    def create_widgets(self):
        # Create notebook for tabs
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill='both', expand=True, padx=10, pady=10)

        # Training tab
        training_frame = ttk.Frame(notebook)
        notebook.add(training_frame, text='Training')

        # Configuration tab
        config_frame = ttk.Frame(notebook)
        notebook.add(config_frame, text='Configuration')

        # Analysis tab
        analysis_frame = ttk.Frame(notebook)
        notebook.add(analysis_frame, text='Analysis')

        self.create_training_tab(training_frame)
        self.create_config_tab(config_frame)
        self.create_analysis_tab(analysis_frame)

    def create_training_tab(self, parent):
        # File Selection
        file_frame = ttk.LabelFrame(parent, text="File Selection")
        file_frame.pack(fill='x', padx=5, pady=5)

        # Training folder
        ttk.Label(file_frame, text="Training PGN Folder:").grid(row=0, column=0, sticky='w', padx=5, pady=2)
        ttk.Entry(file_frame, textvariable=self.training_folder, width=50).grid(row=0, column=1, padx=5, pady=2)
        ttk.Button(file_frame, text="Browse", command=self.browse_training_folder).grid(row=0, column=2, padx=5, pady=2)

        # Validation folder
        ttk.Label(file_frame, text="Validation PGN Folder:").grid(row=1, column=0, sticky='w', padx=5, pady=2)
        ttk.Entry(file_frame, textvariable=self.validation_folder, width=50).grid(row=1, column=1, padx=5, pady=2)
        ttk.Button(file_frame, text="Browse", command=self.browse_validation_folder).grid(row=1, column=2, padx=5, pady=2)

        # Controls
        control_frame = ttk.LabelFrame(parent, text="Controls")
        control_frame.pack(fill='x', padx=5, pady=5)

        ttk.Button(control_frame, text="Start Training", command=self.start_training).pack(side='left', padx=5, pady=5)
        ttk.Button(control_frame, text="Stop Training", command=self.stop_training).pack(side='left', padx=5, pady=5)
        ttk.Button(control_frame, text="Load Checkpoint", command=self.load_checkpoint).pack(side='left', padx=5, pady=5)
        ttk.Button(control_frame, text="Save Checkpoint", command=self.save_checkpoint).pack(side='left', padx=5, pady=5)
        ttk.Button(control_frame, text="Reset Progress", command=self.reset_progress).pack(side='left', padx=5, pady=5)

        # Progress
        progress_frame = ttk.LabelFrame(parent, text="Progress")
        progress_frame.pack(fill='x', padx=5, pady=5)

        self.progress_label = ttk.Label(progress_frame, text="Ready to train...")
        self.progress_label.pack(pady=10)

        # Training Progress Chart
        chart_frame = ttk.LabelFrame(parent, text="Training Progress")
        chart_frame.pack(fill='both', expand=True, padx=5, pady=5)

        self.fig, self.ax = plt.subplots(figsize=(10, 4))
        self.ax.set_title("Training and Validation Loss")
        self.ax.set_xlabel("Epoch")
        self.ax.set_ylabel("Loss")
        self.ax.grid(True)

        self.canvas = FigureCanvasTkAgg(self.fig, chart_frame)
        self.canvas.get_tk_widget().pack(fill='both', expand=True)

    def create_config_tab(self, parent):
        # Training Parameters
        param_frame = ttk.LabelFrame(parent, text="Training Parameters")
        param_frame.pack(fill='x', padx=5, pady=5)

        # Create parameter fields
        params = [
            ("Epochs:", self.epochs),
            ("Batch Size:", self.batch_size),
            ("Learning Rate:", self.learning_rate),
            ("Weight Decay:", self.weight_decay),
            ("Validation Interval:", self.validation_interval),
            ("Checkpoint Interval:", self.checkpoint_interval),
            ("Early Stopping Patience:", self.early_stopping_patience)
        ]

        for i, (label, var) in enumerate(params):
            ttk.Label(param_frame, text=label).grid(row=i, column=0, sticky='w', padx=5, pady=2)
            ttk.Entry(param_frame, textvariable=var, width=20).grid(row=i, column=1, padx=5, pady=2)
        
        current_row = len(params)
        ttk.Label(param_frame, text="Auto-save Best Model:").grid(row=current_row, column=0, sticky='w', padx=5, pady=2)
        ttk.Checkbutton(param_frame, variable=self.auto_save_best).grid(row=current_row, column=1, sticky='w', padx=5, pady=2)
        
        current_row += 1
        ttk.Label(param_frame, text="Auto-load on Startup:").grid(row=current_row, column=0, sticky='w', padx=5, pady=2)
        ttk.Checkbutton(param_frame, variable=self.auto_load_checkpoint).grid(row=current_row, column=1, sticky='w', padx=5, pady=2)

        current_row += 1
        ttk.Button(param_frame, text="Apply Configuration", command=self.apply_config).grid(row=current_row, column=0, columnspan=2, pady=10)

        # Device info
        device_frame = ttk.LabelFrame(parent, text="Device Information")
        device_frame.pack(fill='x', padx=5, pady=5)

        device_text = f"Device: {self.chess_model.device}"
        if torch.cuda.is_available():
            device_text += f"\nGPU: {torch.cuda.get_device_name()}"
            if hasattr(torch.cuda, 'get_device_properties'):
                 device_text += f"\nCUDA Memory: {torch.cuda.get_device_properties(0).total_memory // 1024**3} GB"

        ttk.Label(device_frame, text=device_text).pack(padx=5, pady=5)

    def create_analysis_tab(self, parent):
        # Analysis tools
        analysis_frame = ttk.LabelFrame(parent, text="Model Analysis")
        analysis_frame.pack(fill='x', padx=5, pady=5)

        ttk.Button(analysis_frame, text="Test Move Prediction", command=self.test_move_prediction).pack(side='left', padx=5, pady=5)
        ttk.Button(analysis_frame, text="Evaluate on Test Set", command=self.evaluate_test_set).pack(side='left', padx=5, pady=5)
        ttk.Button(analysis_frame, text="Export Model", command=self.export_model).pack(side='left', padx=5, pady=5)

        # Results display
        self.results_text = tk.Text(parent, height=20, width=80)
        self.results_text.pack(fill='both', expand=True, padx=5, pady=5)

        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=self.results_text.yview)
        scrollbar.pack(side="right", fill="y")
        self.results_text.configure(yscrollcommand=scrollbar.set)

    def browse_training_folder(self):
        folder = filedialog.askdirectory()
        if folder:
            self.training_folder.set(folder)

    def browse_validation_folder(self):
        folder = filedialog.askdirectory()
        if folder:
            self.validation_folder.set(folder)

    def start_training(self):
        if self.is_training:
            messagebox.showwarning("Warning", "Training is already in progress!")
            return

        if not self.training_folder.get():
            messagebox.showerror("Error", "Please select a training folder!")
            return

        self.is_training = True
        self.training_thread = threading.Thread(target=self.train_model_thread, daemon=True)
        self.training_thread.start()

    def stop_training(self):
        self.is_training = False

    def train_model_thread(self):
        try:
            # Load training data
            self.update_progress("Loading training data...")
            train_data = self.chess_model.parse_pgn_data(
                self.training_folder.get(),
                max_games=5000,
                progress_callback=self.data_loading_progress
            )

            if len(train_data[0][0]) == 0:
                self.root.after(0, lambda: messagebox.showerror("Error", "No training data found!"))
                self.is_training = False
                return

            # Load validation data if available
            val_data = None
            if self.validation_folder.get():
                self.update_progress("Loading validation data...")
                val_data = self.chess_model.parse_pgn_data(
                    self.validation_folder.get(),
                    max_games=1000,
                    progress_callback=self.data_loading_progress
                )

            # Train model
            self.update_progress("Starting training...")
            self.chess_model.train_model(
                train_data=train_data,
                val_data=val_data,
                epochs=self.epochs.get(),
                batch_size=self.batch_size.get(),
                learning_rate=self.learning_rate.get(),
                weight_decay=self.weight_decay.get(),
                progress_callback=self.training_progress_callback,
                auto_save_best=self.auto_save_best.get(),
                checkpoint_interval=self.checkpoint_interval.get(),
                checkpoint_dir=self.checkpoint_dir.get()
            )
            
            if self.is_training: # Check if training was not stopped manually
                 self.root.after(0, lambda: messagebox.showinfo("Success", "Training completed!"))

        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("Error", f"Training failed: {str(e)}"))
        finally:
            self.is_training = False
            self.update_progress("Ready.")

    def data_loading_progress(self, current, total):
        progress = f"Loading data: {current}/{total} games"
        self.root.after(0, lambda: self.update_progress(progress))

    def training_progress_callback(self, progress_info):
        if not self.is_training:
            # Stop the training loop if is_training is set to False
            raise Exception("Training stopped by user.")
            
        epoch = progress_info['epoch']
        total_epochs = progress_info['total_epochs']
        train_loss = progress_info['train_loss']
        train_acc = progress_info['train_accuracy']
        val_loss = progress_info['val_loss']
        val_acc = progress_info['val_accuracy']

        progress_text = f"Epoch {epoch}/{total_epochs} - "
        progress_text += f"Train Loss: {train_loss:.4f} (Acc: {train_acc:.2f}%)"
        if val_loss > 0:
            progress_text += f" - Val Loss: {val_loss:.4f} (Acc: {val_acc:.2f}%)"

        self.root.after(0, lambda: self.update_progress(progress_text))
        self.root.after(0, self.update_training_plot)

    def update_progress(self, message):
        self.progress_label.config(text=message)
        self.root.update()

    def update_training_plot(self):
        self.ax.clear()
        
        if self.chess_model.train_losses:
            epochs = range(1, len(self.chess_model.train_losses) + 1)
            self.ax.plot(epochs, self.chess_model.train_losses, 'b-', label='Train Loss')

        if self.chess_model.val_losses:
            # Ensure validation epochs match training epochs length for plotting
            val_epochs = range(1, len(self.chess_model.val_losses) + 1)
            self.ax.plot(val_epochs, self.chess_model.val_losses, 'r-', label='Validation Loss')

        self.ax.set_title('Training and Validation Loss')
        self.ax.set_xlabel('Epoch')
        self.ax.set_ylabel('Loss')
        self.ax.legend()
        self.ax.grid(True)
        self.canvas.draw()

    def load_checkpoint(self):
        file_path = filedialog.askopenfilename(
            title="Load Model Checkpoint",
            filetypes=[("PyTorch files", "*.pth"), ("All files", "*.*")]
        )
        if file_path:
            try:
                self.chess_model.load_model(file_path)
                self.update_training_plot()
                messagebox.showinfo("Success", "Model loaded successfully!")
                self.update_progress(f"Loaded from {os.path.basename(file_path)}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load model: {str(e)}")

    def save_checkpoint(self):
        if self.chess_model.model is None:
            messagebox.showerror("Error", "No model to save!")
            return

        file_path = filedialog.asksaveasfilename(
            title="Save Model Checkpoint",
            defaultextension=".pth",
            filetypes=[("PyTorch files", "*.pth"), ("All files", "*.*")],
            initialdir=self.checkpoint_dir.get()
        )
        if file_path:
            try:
                self.chess_model.save_model(file_path)
                messagebox.showinfo("Success", "Model saved successfully!")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save model: {str(e)}")

    def reset_progress(self):
        if messagebox.askyesno("Confirm", "Are you sure you want to reset all training progress?"):
            self.ax.clear()
            self.ax.set_title("Training and Validation Loss")
            self.ax.set_xlabel("Epoch")
            self.ax.set_ylabel("Loss")
            self.ax.grid(True)
            self.canvas.draw()
            self.update_progress("Progress reset")

            # Clear training history and model
            self.chess_model.train_losses = []
            self.chess_model.val_losses = []
            self.chess_model.model = None

    def apply_config(self):
        messagebox.showinfo("Success", "Configuration applied!")

    def auto_load_on_startup(self):
        """Auto-load the latest checkpoint on startup if enabled"""
        if not self.auto_load_checkpoint.get():
            return

        checkpoint_dir = self.checkpoint_dir.get()
        if not os.path.exists(checkpoint_dir):
            return

        # Look for the best model first, then latest checkpoint
        best_model_path = os.path.join(checkpoint_dir, "best_model.pth")
        if os.path.exists(best_model_path):
            try:
                self.chess_model.load_model(best_model_path)
                self.update_training_plot()
                self.update_progress("Auto-loaded best model")
                return
            except Exception as e:
                print(f"Failed to auto-load best model: {e}")

        # Look for latest checkpoint
        latest_checkpoint = self.find_latest_checkpoint(checkpoint_dir)
        if latest_checkpoint:
            try:
                self.chess_model.load_model(latest_checkpoint)
                self.update_training_plot()
                self.update_progress(f"Auto-loaded {os.path.basename(latest_checkpoint)}")
            except Exception as e:
                print(f"Failed to auto-load checkpoint: {e}")

    def find_latest_checkpoint(self, checkpoint_dir):
        """Find the latest checkpoint in the directory"""
        if not os.path.exists(checkpoint_dir):
            return None

        checkpoint_files = [f for f in os.listdir(checkpoint_dir) if f.startswith("checkpoint_epoch_") and f.endswith(".pth")]
        if not checkpoint_files:
            return None

        # Sort by epoch number and return the latest
        try:
            checkpoint_files.sort(key=lambda x: int(x.split("_")[2].split(".")[0]), reverse=True)
            return os.path.join(checkpoint_dir, checkpoint_files[0])
        except (IndexError, ValueError):
            # Handle cases with malformed checkpoint filenames
            return None


    def test_move_prediction(self):
        if self.chess_model.model is None:
            messagebox.showerror("Error", "No model loaded!")
            return

        # Test on starting position
        board = chess.Board()
        predicted_move = self.chess_model.predict_move(board)

        result = f"Test Position: {board.fen()}\n"
        result += f"Predicted Move: {predicted_move}\n"
        result += f"Legal Moves: {len(list(board.legal_moves))}\n"

        # Test on a few more positions
        test_positions = [
            "rnbqkbnr/pppppppp/8/8/4P3/8/PPPP1PPP/RNBQKBNR b KQkq e3 0 1",  # After 1.e4
            "rnbqkbnr/pppp1ppp/8/4p3/4P3/8/PPPP1PPP/RNBQKBNR w KQkq e6 0 2",  # After 1.e4 e5
            "r1bqkbnr/pppp1ppp/2n5/4p3/4P3/5N2/PPPP1PPP/RNBQKB1R w KQkq - 2 3"  # After 1.e4 e5 2.Nf3 Nc6
        ]

        for i, fen in enumerate(test_positions):
            try:
                test_board = chess.Board(fen)
                test_move = self.chess_model.predict_move(test_board)
                result += f"\nTest Position {i+2}: {fen}\n"
                result += f"Predicted Move: {test_move}\n"
                result += f"Legal Moves: {len(list(test_board.legal_moves))}\n"
            except Exception as e:
                result += f"\nError in test position {i+2}: {str(e)}\n"

        self.results_text.delete(1.0, tk.END)
        self.results_text.insert(tk.END, result)

    def evaluate_test_set(self):
        if self.chess_model.model is None:
            messagebox.showerror("Error", "No model loaded!")
            return

        if not self.validation_folder.get():
            messagebox.showerror("Error", "Please select a validation folder for testing!")
            return

        try:
            # Load test data
            self.update_progress("Loading test data...")
            test_data = self.chess_model.parse_pgn_data(
                self.validation_folder.get(),
                max_games=500  # Smaller test set
            )

            (X_test, masks_test), y_test = test_data

            if len(X_test) == 0:
                messagebox.showerror("Error", "No test data found!")
                return

            # Create test dataset
            test_dataset = ChessDataset(X_test, masks_test, y_test)
            test_loader = DataLoader(test_dataset, batch_size=self.batch_size.get(), shuffle=False)

            # Evaluate model
            self.chess_model.model.eval()
            test_loss = 0.0
            correct = 0
            total = 0
            criterion = nn.CrossEntropyLoss()

            with torch.no_grad():
                for batch in test_loader:
                    board = batch['board'].to(self.chess_model.device)
                    mask = batch['mask'].to(self.chess_model.device)
                    target = batch['target'].to(self.chess_model.device)

                    output = self.chess_model.model(board, mask)
                    loss = criterion(output, target)
                    test_loss += loss.item()

                    _, predicted = torch.max(output.data, 1)
                    _, targets = torch.max(target.data, 1)
                    total += target.size(0)
                    correct += (predicted == targets).sum().item()

            test_loss = test_loss / len(test_loader)
            test_accuracy = 100 * correct / total

            result = f"Test Set Evaluation Results:\n"
            result += f"Test Loss: {test_loss:.4f}\n"
            result += f"Test Accuracy: {test_accuracy:.2f}%\n"
            result += f"Total Positions: {total}\n"
            result += f"Correct Predictions: {correct}\n"

            self.results_text.delete(1.0, tk.END)
            self.results_text.insert(tk.END, result)

        except Exception as e:
            messagebox.showerror("Error", f"Evaluation failed: {str(e)}")
        finally:
            self.update_progress("Evaluation completed")

    def export_model(self):
        if self.chess_model.model is None:
            messagebox.showerror("Error", "No model to export!")
            return

        file_path = filedialog.asksaveasfilename(
            title="Export Model",
            defaultextension=".pth",
            filetypes=[("PyTorch files", "*.pth"), ("ONNX files", "*.onnx"), ("All files", "*.*")]
        )
        if file_path:
            try:
                if file_path.endswith('.onnx'):
                    # Export to ONNX format
                    dummy_board = torch.randn(1, 12, 8, 8).to(self.chess_model.device)
                    dummy_mask = torch.randn(1, len(self.chess_model.move_to_index)).to(self.chess_model.device)

                    torch.onnx.export(
                        self.chess_model.model,
                        (dummy_board, dummy_mask),
                        file_path,
                        export_params=True,
                        opset_version=11,
                        do_constant_folding=True,
                        input_names=['board', 'mask'],
                        output_names=['move_probabilities']
                    )
                else:
                    # Export as PyTorch model
                    self.chess_model.save_model(file_path)

                messagebox.showinfo("Success", "Model exported successfully!")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to export model: {str(e)}")

def main():
    # Check if required packages are available
    try:
        import torch
        import chess
        import chess.pgn
        print(f"PyTorch version: {torch.__version__}")
        print(f"CUDA available: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"CUDA device: {torch.cuda.get_device_name()}")
    except ImportError as e:
        print(f"Missing required package: {e}")
        print("Please install required packages:")
        print("pip install torch torchvision chess matplotlib")
        return

    root = tk.Tk()
    app = ChessTrainerGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()