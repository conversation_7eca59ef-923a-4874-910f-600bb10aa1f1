# chess_player_gui.py
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import chess
import chess.engine
import chess.pgn
import torch
import numpy as np
from datetime import datetime
import threading
import time
from model import ChessNet
from utils import board_to_planes_fast, MOVE_TO_IDX, IDX_TO_MOVE

class ChessPlayerGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("♟️ Chess AI Player vs Stockfish")
        self.root.geometry("900x700") # Slightly wider for top moves display
        # Game state
        self.board = chess.Board()
        self.game = chess.pgn.Game()
        self.node = self.game
        self.engine = None
        self.model = None
        self.device = torch.device("cpu")
        self.game_active = False
        self.setup_ui()

    # --- MCTS Integration ---
    class Node:
        def __init__(self, board, prior=0):
            self.board = board.copy()
            self.prior = prior
            self.children = {}
            self.visits = 0
            self.value_sum = 0

        def value(self):
            return 0 if self.visits == 0 else self.value_sum / self.visits

        def is_terminal(self):
            return self.board.is_game_over()

    def evaluate_board(self, board):
        """
        Run the neural network on a board state.
        Returns: (policy_logits, value)
        """
        if not self.model:
            # Return uniform policy and 0 value if no model
            return torch.zeros((1, len(MOVE_TO_IDX))), 0.0
        try:
            planes = board_to_planes_fast(board)
            x = torch.tensor(planes).unsqueeze(0).to(self.device)
            with torch.no_grad():
                policy_logits, value = self.model(x)
            return policy_logits, value.item()
        except Exception as e:
            print(f"NN eval error: {e}")
            return torch.zeros((1, len(MOVE_TO_IDX))), 0.0

    def mcts_search(self, num_sim=50, cpuct=1.0, return_stats=False):
        """
        Run MCTS and return the best move and optionally stats.
        """
        if not self.model:
            return (None, {}) if return_stats else None

        root = self.Node(self.board)
        stats = {} # To store move -> visits

        for _ in range(num_sim):
            node = root
            search_path = [node]

            # Selection
            while node.children:
                children = list(node.children.values())
                uct_scores = [
                    child.value() + cpuct * child.prior * np.sqrt(node.visits) / (1 + child.visits)
                    for child in children
                ]
                best_idx = np.argmax(uct_scores)
                node = children[best_idx]
                search_path.append(node)

            # Expansion & Evaluation
            if not node.is_terminal():
                policy_logits, value = self.evaluate_board(node.board)
                policy = torch.softmax(policy_logits, dim=-1).squeeze().numpy()

                legal_moves = list(node.board.legal_moves)
                for move in legal_moves:
                    uci = move.uci()
                    prior = policy[MOVE_TO_IDX[uci]] if uci in MOVE_TO_IDX else 0
                    child_node = self.Node(node.board, prior=prior)
                    child_node.board.push(move)
                    node.children[move] = child_node
            else:
                # Terminal node: get result
                result = node.board.result()
                value = 1.0 if result == "1-0" else 0.0 if result == "0-1" else 0.5

            # Backpropagation
            for node in search_path:
                node.visits += 1
                # Value from current player's perspective
                node.value_sum += value if node.board.turn == self.board.turn else 1 - value

        # Prepare stats if requested
        if return_stats:
            for move, child in root.children.items():
                stats[move.uci()] = child.visits

        # Choose move with most visits
        if not root.children:
            return (None, stats) if return_stats else None
        best_move = max(root.children.keys(), key=lambda m: root.children[m].visits)
        return (best_move, stats) if return_stats else best_move
    # --- End MCTS Integration ---

    def setup_ui(self):
        # Main frame
        main_frame = tk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        # Left panel for controls
        control_frame = tk.Frame(main_frame)
        control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        # Right panel for board display and top moves
        right_frame = tk.Frame(main_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # Board display frame
        board_frame = tk.Frame(right_frame)
        board_frame.pack(side=tk.TOP, fill=tk.BOTH, expand=True)
        
        # Top moves display frame
        moves_frame = tk.Frame(right_frame)
        moves_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=(5, 0))
        tk.Label(moves_frame, text="Top Moves (MCTS):", font=("Helvetica", 10, "bold")).pack(anchor='w')
        self.top_moves_text = tk.Text(moves_frame, height=6, width=50, font=("Courier", 10))
        self.top_moves_text.pack(fill=tk.X)

        # === CONTROL PANEL ===
        row = 0
        # Load Stockfish
        tk.Button(control_frame, text="🐟 Load Stockfish.exe", command=self.load_stockfish, bg="lightblue").grid(row=row, column=0, columnspan=2, pady=5, sticky='ew')
        self.stockfish_label = tk.Label(control_frame, text="❌ Not loaded", fg="red")
        self.stockfish_label.grid(row=row+1, column=0, columnspan=2, sticky='w')
        row += 2
        # Load AI Model
        tk.Button(control_frame, text="🤖 Load AI Model (.pt)", command=self.load_model, bg="lightgreen").grid(row=row, column=0, columnspan=2, pady=5, sticky='ew')
        self.model_label = tk.Label(control_frame, text="❌ Not loaded", fg="red")
        self.model_label.grid(row=row+1, column=0, columnspan=2, sticky='w')
        row += 2
        # FEN Input
        tk.Label(control_frame, text="📋 FEN Position:").grid(row=row, column=0, columnspan=2, sticky='w')
        self.fen_entry = tk.Entry(control_frame, width=40)
        self.fen_entry.grid(row=row+1, column=0, columnspan=2, pady=2, sticky='ew')
        tk.Button(control_frame, text="Load FEN", command=self.load_fen).grid(row=row+2, column=0, pady=2, sticky='ew')
        tk.Button(control_frame, text="Reset Board", command=self.reset_board).grid(row=row+2, column=1, pady=2, sticky='ew')
        row += 3
        # Game controls
        tk.Label(control_frame, text="🎮 Game Controls:").grid(row=row, column=0, columnspan=2, sticky='w')
        # Player selection
        player_frame = tk.Frame(control_frame)
        player_frame.grid(row=row+1, column=0, columnspan=2, sticky='ew', pady=2)
        tk.Label(player_frame, text="You play as:").pack(side=tk.LEFT)
        self.player_color = tk.StringVar(value="white")
        tk.Radiobutton(player_frame, text="White", variable=self.player_color, value="white").pack(side=tk.LEFT)
        tk.Radiobutton(player_frame, text="Black", variable=self.player_color, value="black").pack(side=tk.LEFT)
        # Game mode selection
        mode_frame = tk.Frame(control_frame)
        mode_frame.grid(row=row+2, column=0, columnspan=2, sticky='ew', pady=2)
        tk.Label(mode_frame, text="Game Mode:").pack(side=tk.LEFT)
        self.game_mode = tk.StringVar(value="human_vs_stockfish")
        tk.Radiobutton(mode_frame, text="Human vs Stockfish", variable=self.game_mode, value="human_vs_stockfish").pack(side=tk.LEFT)
        tk.Radiobutton(mode_frame, text="Human vs AI", variable=self.game_mode, value="human_vs_ai").pack(side=tk.LEFT)
        tk.Radiobutton(mode_frame, text="AI vs Stockfish", variable=self.game_mode, value="ai_vs_stockfish").pack(side=tk.LEFT)
        row += 3
        # AI vs Stockfish settings
        ai_settings_frame = tk.Frame(control_frame)
        ai_settings_frame.grid(row=row, column=0, columnspan=2, sticky='ew', pady=2)
        tk.Label(ai_settings_frame, text="AI vs Stockfish:").pack(side=tk.LEFT)
        self.ai_color = tk.StringVar(value="white")
        tk.Radiobutton(ai_settings_frame, text="AI=White", variable=self.ai_color, value="white").pack(side=tk.LEFT)
        tk.Radiobutton(ai_settings_frame, text="AI=Black", variable=self.ai_color, value="black").pack(side=tk.LEFT)
        row += 1
        # Stockfish time control
        time_frame = tk.Frame(control_frame)
        time_frame.grid(row=row, column=0, columnspan=2, sticky='ew', pady=2)
        tk.Label(time_frame, text="Stockfish time (sec):").pack(side=tk.LEFT)
        self.stockfish_time = tk.Entry(time_frame, width=5)
        self.stockfish_time.insert(0, "1.0")
        self.stockfish_time.pack(side=tk.LEFT, padx=5)
        row += 1
        # === MCTS Settings ===
        tk.Label(control_frame, text="🧠 MCTS Settings:", font=("Helvetica", 10, "bold")).grid(row=row, column=0, columnspan=2, sticky='w', pady=(10,2))
        row += 1
        # MCTS sims
        tk.Label(control_frame, text="Simulations:").grid(row=row, column=0, sticky='w')
        self.mcts_sims_entry = tk.Entry(control_frame, width=8)
        self.mcts_sims_entry.insert(0, "50")
        self.mcts_sims_entry.grid(row=row, column=1, sticky='w', padx=5)
        row += 1
        # Cpuct
        tk.Label(control_frame, text="CPUCT:").grid(row=row, column=0, sticky='w')
        self.cpuct_entry = tk.Entry(control_frame, width=8)
        self.cpuct_entry.insert(0, "1.0")
        self.cpuct_entry.grid(row=row, column=1, sticky='w', padx=5)
        row += 1
        # Top moves to display
        tk.Label(control_frame, text="Top Moves to Show:").grid(row=row, column=0, sticky='w')
        self.top_moves_count_entry = tk.Entry(control_frame, width=8)
        self.top_moves_count_entry.insert(0, "5")
        self.top_moves_count_entry.grid(row=row, column=1, sticky='w', padx=5)
        row += 1
        # Start/Stop game
        tk.Button(control_frame, text="▶️ Start Game", command=self.start_game, bg="lightgreen").grid(row=row, column=0, pady=5, sticky='ew')
        tk.Button(control_frame, text="⏹️ Stop Game", command=self.stop_game, bg="lightcoral").grid(row=row, column=1, pady=5, sticky='ew')
        row += 1
        # Move input
        tk.Label(control_frame, text="Your move (e.g. e2e4):").grid(row=row, column=0, columnspan=2, sticky='w')
        self.move_entry = tk.Entry(control_frame, width=20)
        self.move_entry.grid(row=row+1, column=0, pady=2, sticky='ew')
        self.move_entry.bind('<Return>', self.make_move)
        tk.Button(control_frame, text="Move", command=self.make_move).grid(row=row+1, column=1, pady=2, sticky='ew')
        row += 2
        # AI Prediction
        tk.Button(control_frame, text="🔮 Get AI Prediction", command=self.get_ai_prediction, bg="lightyellow").grid(row=row, column=0, columnspan=2, pady=5, sticky='ew')
        self.prediction_label = tk.Label(control_frame, text="", fg="blue", wraplength=200)
        self.prediction_label.grid(row=row+1, column=0, columnspan=2, sticky='w')
        row += 2
        # Save game
        tk.Button(control_frame, text="💾 Save Game as PGN", command=self.save_pgn, bg="lightgray").grid(row=row, column=0, columnspan=2, pady=5, sticky='ew')
        # Configure column weights
        control_frame.columnconfigure(0, weight=1)
        control_frame.columnconfigure(1, weight=1)
        # === BOARD DISPLAY ===
        self.board_text = tk.Text(board_frame, font=("Courier", 12), width=50, height=25)
        self.board_text.pack(fill=tk.BOTH, expand=True)
        # Status bar
        self.status_label = tk.Label(self.root, text="Ready to play chess!", relief=tk.SUNKEN, anchor=tk.W)
        self.status_label.pack(side=tk.BOTTOM, fill=tk.X)
        # Initial board display
        self.update_board_display()

    def load_stockfish(self):
        path = filedialog.askopenfilename(
            title="Select Stockfish executable",
            filetypes=[("Executable files", "*.exe"), ("All files", "*.*")]
        )
        if path:
            try:
                if self.engine:
                    self.engine.quit()
                self.engine = chess.engine.SimpleEngine.popen_uci(path)
                self.stockfish_label.config(text="✅ Stockfish loaded", fg="green")
                self.status_label.config(text=f"Stockfish loaded from: {path}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load Stockfish:\n{str(e)}")

    def load_model(self):
        path = filedialog.askopenfilename(
            title="Select AI model checkpoint",
            filetypes=[("PyTorch files", "*.pt"), ("All files", "*.*")]
        )
        if path:
            try:
                self.model = ChessNet(num_moves=len(MOVE_TO_IDX))
                checkpoint = torch.load(path, map_location=self.device)
                self.model.load_state_dict(checkpoint['model_state_dict'])
                self.model.eval()
                self.model_label.config(text="✅ AI Model loaded", fg="green")
                self.status_label.config(text=f"AI model loaded from: {path}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load AI model:\n{str(e)}")

    def load_fen(self):
        fen = self.fen_entry.get().strip()
        if fen:
            try:
                self.board = chess.Board(fen)
                self.update_board_display()
                self.status_label.config(text=f"Loaded FEN: {fen}")
            except Exception as e:
                messagebox.showerror("Error", f"Invalid FEN:\n{str(e)}")

    def reset_board(self):
        self.board = chess.Board()
        self.game = chess.pgn.Game()
        self.node = self.game
        self.update_board_display()
        self.status_label.config(text="Board reset to starting position")

    def update_board_display(self):
        board_str = str(self.board)
        self.board_text.delete(1.0, tk.END)
        self.board_text.insert(1.0, board_str)
        self.board_text.insert(tk.END, f"\nFEN: {self.board.fen()}")
        self.board_text.insert(tk.END, f"\nTurn: {'White' if self.board.turn else 'Black'}")
        if self.board.is_checkmate():
            winner = "Black" if self.board.turn else "White"
            self.board_text.insert(tk.END, f"\n🏆 CHECKMATE! {winner} wins!")
        elif self.board.is_stalemate():
            self.board_text.insert(tk.END, f"\n🤝 STALEMATE! Draw!")
        elif self.board.is_check():
            self.board_text.insert(tk.END, f"\n⚠️ CHECK!")

    def start_game(self):
        game_mode = self.game_mode.get()
        # Check requirements based on game mode
        if game_mode in ["human_vs_stockfish", "ai_vs_stockfish"] and not self.engine:
            messagebox.showerror("Error", "Please load Stockfish first!")
            return
        if game_mode in ["human_vs_ai", "ai_vs_stockfish"] and not self.model:
            messagebox.showerror("Error", "Please load AI model first!")
            return
        self.game_active = True
        if game_mode == "ai_vs_stockfish":
            self.status_label.config(text="AI vs Stockfish game started! Watch them play...")
            # Start automated game
            threading.Thread(target=self.run_ai_vs_stockfish, daemon=True).start()
        else:
            self.status_label.config(text="Game started! Make your move.")
            # If player is black, make opponent move first
            if self.player_color.get() == "black":
                threading.Thread(target=self.make_opponent_move, daemon=True).start()

    def stop_game(self):
        self.game_active = False
        self.status_label.config(text="Game stopped.")

    def make_move(self, event=None):
        if not self.game_active:
            return
        move_str = self.move_entry.get().strip()
        if not move_str:
            return
        try:
            move = chess.Move.from_uci(move_str)
            if move in self.board.legal_moves:
                self.board.push(move)
                self.node = self.node.add_variation(move)
                self.move_entry.delete(0, tk.END)
                self.update_board_display()
                if self.board.is_game_over():
                    self.game_active = False
                    self.status_label.config(text="Game over!")
                else:
                    # Make opponent move
                    threading.Thread(target=self.make_opponent_move, daemon=True).start()
            else:
                messagebox.showerror("Error", "Illegal move!")
        except Exception as e:
            messagebox.showerror("Error", f"Invalid move format:\n{str(e)}")

    def make_opponent_move(self):
        if not self.game_active or self.board.is_game_over():
            return
        try:
            game_mode = self.game_mode.get()
            if game_mode == "human_vs_stockfish" and self.engine:
                result = self.engine.play(self.board, chess.engine.Limit(time=1.0))
                move = result.move
            elif game_mode == "human_vs_ai" and self.model:
                move = self.get_ai_move()
            else:
                return
            if move:
                self.board.push(move)
                self.node = self.node.add_variation(move)
                self.root.after(0, self.update_board_display)
                self.root.after(0, lambda: self.status_label.config(text=f"Opponent played: {move}"))
                if self.board.is_game_over():
                    self.game_active = False
                    self.root.after(0, lambda: self.status_label.config(text="Game over!"))
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("Error", f"Opponent move failed:\n{str(e)}"))

    def run_ai_vs_stockfish(self):
        """Run an automated game between AI and Stockfish"""
        try:
            move_count = 0
            max_moves = 200  # Prevent infinite games
            ai_is_white = self.ai_color.get() == "white"
            # Get Stockfish time setting
            try:
                stockfish_time = float(self.stockfish_time.get())
            except:
                stockfish_time = 1.0
            while self.game_active and not self.board.is_game_over() and move_count < max_moves:
                if (self.board.turn == chess.WHITE and ai_is_white) or (self.board.turn == chess.BLACK and not ai_is_white):
                    # AI's turn
                    move = self.get_ai_move()
                    player_name = f"AI ({'White' if ai_is_white else 'Black'})"
                else:
                    # Stockfish's turn
                    result = self.engine.play(self.board, chess.engine.Limit(time=stockfish_time))
                    move = result.move
                    player_name = f"Stockfish ({'White' if not ai_is_white else 'Black'})"
                if move:
                    self.board.push(move)
                    self.node = self.node.add_variation(move)
                    move_count += 1
                    # Update GUI
                    self.root.after(0, self.update_board_display)
                    self.root.after(0, lambda p=player_name, m=move, mc=move_count:
                                   self.status_label.config(text=f"Move {mc}: {p} played {m}"))
                    # Small delay to make moves visible
                    time.sleep(0.5)
                else:
                    break
            # Game ended
            self.game_active = False
            ai_is_white = self.ai_color.get() == "white"
            if self.board.is_checkmate():
                # The side to move is checkmated, so the other side wins
                if self.board.turn == chess.WHITE:
                    winner = f"Stockfish ({'White' if not ai_is_white else 'Black'})"
                else:
                    winner = f"AI ({'White' if ai_is_white else 'Black'})"
                self.root.after(0, lambda w=winner: self.status_label.config(text=f"🏆 CHECKMATE! {w} wins!"))
            elif self.board.is_stalemate():
                self.root.after(0, lambda: self.status_label.config(text="🤝 STALEMATE! Draw!"))
            elif move_count >= max_moves:
                self.root.after(0, lambda: self.status_label.config(text="⏰ Game ended - Move limit reached"))
            else:
                self.root.after(0, lambda: self.status_label.config(text="🏁 Game ended"))
        except Exception as e:
            self.game_active = False
            self.root.after(0, lambda: messagebox.showerror("Error", f"AI vs Stockfish game failed:\n{str(e)}"))

    def get_ai_move(self):
        """Get the best move from MCTS for playing."""
        if not self.model:
            return None
        try:
            # Get MCTS settings
            try:
                sims = int(self.mcts_sims_entry.get())
            except:
                sims = 50
            try:
                cpuct = float(self.cpuct_entry.get())
            except:
                cpuct = 1.0

            move = self.mcts_search(num_sim=sims, cpuct=cpuct, return_stats=False)
            return move
        except Exception as e:
            print(f"MCTS move error: {e}")
            # Fallback to policy or random
            try:
                planes = board_to_planes_fast(self.board)
                x = torch.tensor(planes).unsqueeze(0).to(self.device)
                with torch.no_grad():
                    policy_logits, _ = self.model(x)
                legal_moves = list(self.board.legal_moves)
                if not legal_moves:
                    return None
                move_probs = []
                for move in legal_moves:
                    uci = move.uci()
                    if uci in MOVE_TO_IDX:
                        idx = MOVE_TO_IDX[uci]
                        prob = torch.softmax(policy_logits, dim=1)[0, idx].item()
                        move_probs.append((move, prob))
                if move_probs:
                    best_move = max(move_probs, key=lambda x: x[1])[0]
                    return best_move
                else:
                    return np.random.choice(legal_moves)
            except Exception as e2:
                print(f"AI fallback error: {e2}")
                return np.random.choice(list(self.board.legal_moves)) if self.board.legal_moves else None

    def get_ai_prediction(self):
        """Get AI prediction and display top moves with visit counts."""
        if not self.model:
            messagebox.showerror("Error", "Please load AI model first!")
            return
        try:
            # Get MCTS settings
            try:
                sims = int(self.mcts_sims_entry.get())
            except:
                sims = 50
            try:
                cpuct = float(self.cpuct_entry.get())
            except:
                cpuct = 1.0
            try:
                top_n = int(self.top_moves_count_entry.get())
            except:
                top_n = 5

            # Run MCTS to get move and stats
            best_move, stats = self.mcts_search(num_sim=sims, cpuct=cpuct, return_stats=True)

            # Update prediction label
            if best_move:
                self.prediction_label.config(text=f"AI suggests: {best_move} ({sims} sims)")
            else:
                self.prediction_label.config(text="AI couldn't find a move")

            # Update top moves display
            self.top_moves_text.delete(1.0, tk.END)
            if stats:
                # Sort moves by visits
                sorted_moves = sorted(stats.items(), key=lambda item: item[1], reverse=True)
                top_moves = sorted_moves[:top_n]
                total_visits = sum(stats.values())
                
                lines = [f"Top {top_n} moves from {total_visits} sims:"]
                for move_uci, visits in top_moves:
                    percentage = (visits / total_visits) * 100 if total_visits > 0 else 0
                    lines.append(f"  {move_uci:<6} ({visits:>4} visits, {percentage:>5.1f}%)")
                self.top_moves_text.insert(tk.END, "\n".join(lines))
            else:
                self.top_moves_text.insert(tk.END, "No MCTS stats available.")

        except Exception as e:
            messagebox.showerror("Error", f"Prediction failed:\n{str(e)}")

    def save_pgn(self):
        if not self.game.mainline_moves():
            messagebox.showwarning("Warning", "No moves to save!")
            return
        path = filedialog.asksaveasfilename(
            title="Save game as PGN",
            defaultextension=".pgn",
            filetypes=[("PGN files", "*.pgn"), ("All files", "*.*")]
        )
        if path:
            try:
                # Set game headers
                self.game.headers["Event"] = "AI vs Human"
                self.game.headers["Date"] = datetime.now().strftime("%Y.%m.%d")
                self.game.headers["White"] = "Human" if self.player_color.get() == "white" else "AI"
                self.game.headers["Black"] = "AI" if self.player_color.get() == "white" else "Human"
                with open(path, "w") as f:
                    print(self.game, file=f)
                messagebox.showinfo("Success", f"Game saved to: {path}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save game:\n{str(e)}")

    def __del__(self):
        if self.engine:
            self.engine.quit()

if __name__ == "__main__":
    root = tk.Tk()
    app = ChessPlayerGUI(root)
    root.mainloop()