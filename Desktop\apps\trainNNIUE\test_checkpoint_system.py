#!/usr/bin/env python3

import torch
import os
import shutil
from qwe.chessnet_training import ChessNet, ChessTrainer, Config

def test_checkpoint_system():
    """Test the improved checkpoint system"""
    print("Testing improved checkpoint system...")
    
    # Clean up any existing test checkpoints
    test_checkpoint_dir = "test_checkpoints/"
    if os.path.exists(test_checkpoint_dir):
        shutil.rmtree(test_checkpoint_dir)
    
    # Create a test config
    class TestConfig(Config):
        CHECKPOINT_DIR = test_checkpoint_dir
        SAVE_CHECKPOINT_EVERY = 2
        KEEP_LAST_N_CHECKPOINTS = 3
    
    # Create a simple model
    model = ChessNet()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    
    # Create a trainer instance (without data loaders for testing)
    trainer = ChessTrainer(model, None, None, TestConfig())
    trainer.optimizer = optimizer
    trainer.train_losses = [1.0, 0.9, 0.8, 0.7, 0.6]
    trainer.val_losses = [1.1, 1.0, 0.9, 0.8, 0.7]
    trainer.train_accuracies = [10, 20, 30, 40, 50]
    trainer.val_accuracies = [15, 25, 35, 45, 55]
    
    print("✓ Test setup complete")
    
    # Test 1: Save checkpoint
    print("\n1. Testing checkpoint saving...")
    trainer.save_checkpoint(epoch=5, val_loss=0.7, is_best=True)
    
    if os.path.exists(os.path.join(test_checkpoint_dir, "checkpoint_epoch_005.pth")):
        print("✓ Regular checkpoint saved")
    else:
        print("✗ Regular checkpoint NOT saved")
    
    if os.path.exists(os.path.join(test_checkpoint_dir, "best_model.pth")):
        print("✓ Best model checkpoint saved")
    else:
        print("✗ Best model checkpoint NOT saved")
    
    if os.path.exists(os.path.join(test_checkpoint_dir, "latest_checkpoint.pth")):
        print("✓ Latest checkpoint link saved")
    else:
        print("✗ Latest checkpoint link NOT saved")
    
    # Test 2: Find latest checkpoint
    print("\n2. Testing checkpoint discovery...")
    latest_path = ChessTrainer.find_latest_checkpoint(test_checkpoint_dir)
    if latest_path:
        print(f"✓ Found latest checkpoint: {os.path.basename(latest_path)}")
    else:
        print("✗ Could not find latest checkpoint")
    
    # Test 3: Get checkpoint info
    print("\n3. Testing checkpoint info...")
    if latest_path:
        info = ChessTrainer.get_checkpoint_info(latest_path)
        if 'error' not in info:
            print(f"✓ Checkpoint info retrieved:")
            print(f"  - Epoch: {info.get('epoch')}")
            print(f"  - Best val loss: {info.get('best_val_loss')}")
            print(f"  - Timestamp: {info.get('timestamp')}")
        else:
            print(f"✗ Error getting checkpoint info: {info['error']}")
    
    # Test 4: Save multiple checkpoints and test cleanup
    print("\n4. Testing checkpoint cleanup...")
    for epoch in range(6, 12):  # Save 6 more checkpoints
        trainer.save_checkpoint(epoch=epoch, val_loss=0.6, is_best=False)
    
    # Count remaining checkpoints
    checkpoint_files = [f for f in os.listdir(test_checkpoint_dir) 
                       if f.startswith('checkpoint_epoch_') and f.endswith('.pth')]
    
    print(f"✓ Created multiple checkpoints, {len(checkpoint_files)} regular checkpoints remain")
    
    if len(checkpoint_files) <= TestConfig.KEEP_LAST_N_CHECKPOINTS:
        print("✓ Checkpoint cleanup working correctly")
    else:
        print(f"✗ Too many checkpoints remain ({len(checkpoint_files)} > {TestConfig.KEEP_LAST_N_CHECKPOINTS})")
    
    # Test 5: Load checkpoint
    print("\n5. Testing checkpoint loading...")
    new_model = ChessNet()
    new_optimizer = torch.optim.Adam(new_model.parameters(), lr=0.001)
    new_trainer = ChessTrainer(new_model, None, None, TestConfig())
    new_trainer.optimizer = new_optimizer
    
    result = new_trainer.load_checkpoint(latest_path)
    if result:
        print("✓ Checkpoint loaded successfully")
        success, start_epoch, best_val_loss = result
        print(f"  - Will resume from epoch: {start_epoch}")
        print(f"  - Best validation loss: {best_val_loss}")
    else:
        print("✗ Failed to load checkpoint")
    
    # Cleanup
    print("\n6. Cleaning up test files...")
    if os.path.exists(test_checkpoint_dir):
        shutil.rmtree(test_checkpoint_dir)
        print("✓ Test files cleaned up")
    
    print("\n🎉 Checkpoint system test completed!")

if __name__ == "__main__":
    test_checkpoint_system()
