# train.py
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader
from model import ChessNet
from logs.dataloader import ChessDataset, collate_fn
from utils import MOVE_TO_IDX
import time
import os
from datetime import datetime

def train_epoch(model, dataloader, optimizer, device):
    model.train()
    total_policy_loss = 0.0
    total_value_loss = 0.0
    correct = 0
    total = 0

    for x, y_policy, y_value, legality_mask in dataloader:
        x = x.to(device)
        y_policy = y_policy.to(device)
        y_value = y_value.to(device)
        legality_mask = legality_mask.to(device)

        optimizer.zero_grad()
        policy_logits, value = model(x)
        masked_logits = policy_logits + legality_mask

        policy_loss = F.cross_entropy(masked_logits, y_policy)
        value_loss = F.mse_loss(value.squeeze(), y_value)
        loss = policy_loss + 0.5 * value_loss

        loss.backward()
        torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
        optimizer.step()

        total_policy_loss += policy_loss.item()
        total_value_loss += value_loss.item()

        _, pred = masked_logits.max(1)
        total += y_policy.size(0)
        correct += pred.eq(y_policy).sum().item()

    acc = 100. * correct / total
    return total_policy_loss / len(dataloader), total_value_loss / len(dataloader), acc

def validate(model, dataloader, device):
    model.eval()
    val_policy_loss = 0.0
    val_value_loss = 0.0
    correct = 0
    total = 0

    with torch.no_grad():
        for x, y_policy, y_value, legality_mask in dataloader:
            x = x.to(device)
            y_policy = y_policy.to(device)
            y_value = y_value.to(device)
            legality_mask = legality_mask.to(device)

            policy_logits, value = model(x)
            masked_logits = policy_logits + legality_mask
            policy_loss = F.cross_entropy(masked_logits, y_policy)
            value_loss = F.mse_loss(value.squeeze(), y_value)

            val_policy_loss += policy_loss.item()
            val_value_loss += value_loss.item()

            _, pred = masked_logits.max(1)
            total += y_policy.size(0)
            correct += pred.eq(y_policy).sum().item()

    acc = 100. * correct / total
    return val_policy_loss / len(dataloader), val_value_loss / len(dataloader), acc

def save_checkpoint(model, optimizer, epoch, train_losses, val_losses, path):
    torch.save({
        'epoch': epoch,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'train_losses': train_losses,
        'val_losses': val_losses,
    }, path)
    print(f"✅ Checkpoint saved: {path}")

def train_model(train_loader, val_loader, resume_path=None, num_epochs=10, save_dir="checkpoints"):
    device = torch.device("cpu")
    model = ChessNet(num_moves=len(MOVE_TO_IDX))
    optimizer = torch.optim.AdamW(model.parameters(), lr=3e-4, weight_decay=1e-4)

    start_epoch = 0
    train_losses = {'policy': [], 'value': [], 'acc': []}
    val_losses = {'policy': [], 'value': [], 'acc': []}

    if resume_path:
        print(f"🔁 Loading checkpoint: {resume_path}")
        checkpoint = torch.load(resume_path, map_location=device)
        model.load_state_dict(checkpoint['model_state_dict'])
        optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        start_epoch = checkpoint['epoch'] + 1
        train_losses = checkpoint['train_losses']
        val_losses = checkpoint['val_losses']

    os.makedirs(save_dir, exist_ok=True)
    last_save_time = time.time()
    epoch = start_epoch  # Initialize epoch in case loop doesn't run

    for epoch in range(start_epoch, num_epochs):
        print(f"\nEpoch {epoch+1}/{num_epochs}")
        t0 = time.time()

        train_pl, train_vl, train_acc = train_epoch(model, train_loader, optimizer, device)
        val_pl, val_vl, val_acc = validate(model, val_loader, device)
        t1 = time.time()

        print(f"Train: P={train_pl:.4f}, V={train_vl:.4f}, Acc={train_acc:.2f}% | "
              f"Val: P={val_pl:.4f}, V={val_vl:.4f}, Acc={val_acc:.2f}% | Time: {t1-t0:.1f}s")

        train_losses['policy'].append(train_pl)
        train_losses['value'].append(train_vl)
        train_losses['acc'].append(train_acc)
        val_losses['policy'].append(val_pl)
        val_losses['value'].append(val_vl)
        val_losses['acc'].append(val_acc)

        # Save every 2 epochs
        if (epoch + 1) % 2 == 0:
            save_checkpoint(model, optimizer, epoch, train_losses, val_losses,
                            f"{save_dir}/checkpoint_epoch_{epoch+1}.pt")

        # Save every 20 minutes
        if time.time() - last_save_time > 20 * 60:
            save_checkpoint(model, optimizer, epoch, train_losses, val_losses,
                            f"{save_dir}/checkpoint_latest.pt")
            last_save_time = time.time()

    # Final save
    save_checkpoint(model, optimizer, epoch, train_losses, val_losses,
                    f"{save_dir}/final.pt")
    return model