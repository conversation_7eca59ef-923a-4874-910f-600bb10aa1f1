#!/usr/bin/env python3

try:
    print("Testing imports...")
    
    print("Importing torch...")
    import torch
    print("✓ torch imported successfully")
    
    print("Importing chess...")
    import chess
    print("✓ chess imported successfully")
    
    print("Importing from chessnet_training...")
    from qwe.chessnet_training import ChessNet, parse_pgn_files, ChessDataset, train_epoch, validate
    print("✓ chessnet_training imports successful")
    
    print("All imports successful!")
    
except Exception as e:
    print(f"Import failed: {e}")
    import traceback
    traceback.print_exc()
