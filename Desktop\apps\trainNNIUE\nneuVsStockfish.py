import tkinter as tk
from tkinter import filedialog, messagebox
import chess
import chess.pgn
import subprocess
import torch
import os
from torch import nn
import numpy as np
from datetime import datetime

# === MODEL DEFINITION ===
class ImprovedNNUE(nn.Module):
    def __init__(self, dropout_rate=0.1):
        super().__init__()
        self.feature_transformer = nn.Sequential(
            nn.Linear(776, 512),
            nn.ReLU(),
            nn.Dropout(dropout_rate)
        )
        self.hidden1 = nn.Linear(512, 256)
        self.hidden2 = nn.Linear(256, 256)
        self.hidden3 = nn.Linear(256, 256)
        self.output = nn.Linear(256, 1)
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(dropout_rate)

    def forward(self, x):
        x = self.feature_transformer(x)
        h1 = self.dropout(self.relu(self.hidden1(x)))
        h2 = self.dropout(self.relu(self.hidden2(h1))) + h1
        h3 = self.dropout(self.relu(self.hidden3(h2)))
        return self.output(h3)

def fen_to_features(board: chess.Board) -> np.ndarray:
    piece_to_idx = {'P': 0, 'N': 1, 'B': 2, 'R': 3, 'Q': 4, 'K': 5,
                    'p': 6, 'n': 7, 'b': 8, 'r': 9, 'q': 10, 'k': 11}
    features = np.zeros(776, dtype=np.float32)
    for square in chess.SQUARES:
        piece = board.piece_at(square)
        if piece:
            idx = piece_to_idx[str(piece)] * 64 + square
            features[idx] = 1.0
    features[768] = float(board.turn)
    features[769] = float(board.has_kingside_castling_rights(chess.WHITE))
    features[770] = float(board.has_queenside_castling_rights(chess.WHITE))
    features[771] = float(board.has_kingside_castling_rights(chess.BLACK))
    features[772] = float(board.has_queenside_castling_rights(chess.BLACK))
    features[773] = float(board.ep_square is not None)
    features[774] = min(board.halfmove_clock / 50.0, 1.0)
    features[775] = min(board.fullmove_number / 100.0, 1.0)
    return features

class App:
    def __init__(self, root):
        self.root = root
        self.root.title("Stockfish vs NNUE / NNUE vs Human")

        # File paths
        self.model_path = tk.StringVar()
        self.stockfish_path = tk.StringVar()

        # Engine settings
        self.use_time = tk.BooleanVar(value=True)
        self.time_ms = tk.StringVar(value="200")
        self.depth = tk.StringVar(value="10")
        self.human_as_white = tk.BooleanVar(value=True)
        self.nnue_depth = tk.StringVar(value="3")  # Default NNUE search depth

        # GUI Elements
        tk.Label(root, text="NNUE .pt model").pack()
        tk.Button(root, text="Browse .pt", command=self.browse_model).pack()

        tk.Label(root, text="Stockfish engine").pack()
        tk.Button(root, text="Browse .exe", command=self.browse_stockfish).pack()

        tk.Checkbutton(root, text="Play as White", variable=self.human_as_white).pack()
        tk.Label(root, text="Stockfish Move Type").pack()
        tk.Radiobutton(root, text="Fixed Time (ms)", variable=self.use_time, value=True).pack()
        tk.Entry(root, textvariable=self.time_ms).pack()
        tk.Radiobutton(root, text="Fixed Depth", variable=self.use_time, value=False).pack()
        tk.Entry(root, textvariable=self.depth).pack()
        
        # NNUE search depth control
        tk.Label(root, text="NNUE Search Depth").pack()
        tk.Entry(root, textvariable=self.nnue_depth).pack()

        tk.Button(root, text="Start NNUE vs Human", command=self.play_vs_human).pack(pady=5)
        tk.Button(root, text="Start Stockfish vs NNUE", command=self.play_sf_vs_nnue).pack(pady=5)

    def browse_model(self):
        path = filedialog.askopenfilename(filetypes=[("PyTorch Model", "*.pt")])
        if path: self.model_path.set(path)

    def browse_stockfish(self):
        path = filedialog.askopenfilename(filetypes=[("Stockfish Engine", "*.exe")])
        if path: self.stockfish_path.set(path)

    def start_engine(self):
        engine = subprocess.Popen(self.stockfish_path.get(), stdin=subprocess.PIPE, stdout=subprocess.PIPE, universal_newlines=True, bufsize=1)
        def send(cmd):
            engine.stdin.write(cmd + "\n")
            engine.stdin.flush()
        def read_until(tag="readyok"):
            while True:
                line = engine.stdout.readline()
                if tag in line: break
        send("uci")
        read_until("uciok")
        send("isready")
        read_until("readyok")
        return engine, send, engine.stdout

    def get_stockfish_move(self, board, send, stdout):
        send(f"position fen {board.fen()}")
        if self.use_time.get():
            send(f"go movetime {self.time_ms.get()}")
        else:
            send(f"go depth {self.depth.get()}")
        while True:
            line = stdout.readline()
            if line.startswith("bestmove"):
                return chess.Move.from_uci(line.split()[1])

    def evaluate_position(self, board, model):
        """Evaluate board position using NNUE model"""
        if board.is_checkmate():
            return -100000.0 if board.turn else 100000.0
        elif board.is_game_over():
            return 0.0
            
        input_tensor = torch.FloatTensor(fen_to_features(board)).unsqueeze(0)
        with torch.no_grad():
            score = model(input_tensor).item()
        return score

    def minimax_alpha_beta(self, board, depth, alpha, beta, model, maximizing_player):
        """Minimax with Alpha-Beta pruning using NNUE evaluation"""
        if depth == 0 or board.is_game_over():
            return self.evaluate_position(board, model), None

        legal_moves = list(board.legal_moves)
        best_move = None

        if maximizing_player:
            max_eval = -float('inf')
            for move in legal_moves:
                board.push(move)
                eval, _ = self.minimax_alpha_beta(board, depth-1, alpha, beta, model, False)
                board.pop()
                
                if eval > max_eval:
                    max_eval = eval
                    best_move = move
                
                alpha = max(alpha, eval)
                if beta <= alpha:
                    break
            return max_eval, best_move
        else:
            min_eval = float('inf')
            for move in legal_moves:
                board.push(move)
                eval, _ = self.minimax_alpha_beta(board, depth-1, alpha, beta, model, True)
                board.pop()
                
                if eval < min_eval:
                    min_eval = eval
                    best_move = move
                
                beta = min(beta, eval)
                if beta <= alpha:
                    break
            return min_eval, best_move

    def get_nnue_move(self, board, model):
        """Get NNUE move using minimax with alpha-beta pruning"""
        depth = int(self.nnue_depth.get())
        _, move = self.minimax_alpha_beta(board, depth, -float('inf'), float('inf'), model, maximizing_player=True)
        print(f"NNUE chose {move} (depth={depth})")
        return move

    def play_sf_vs_nnue(self):
        if not self.validate_paths(): return
        model = self.load_model()
        engine, send, stdout = self.start_engine()

        board = chess.Board()
        game = chess.pgn.Game()
        node = game

        while not board.is_game_over():
            if board.turn == chess.WHITE:
                move = self.get_nnue_move(board, model)
            else:
                move = self.get_stockfish_move(board, send, stdout)
            board.push(move)
            node = node.add_variation(move)

        engine.terminate()
        self.save_game(game, "sf_vs_nnue")

    def play_vs_human(self):
        if not self.validate_paths(): return
        model = self.load_model()
        engine, send, stdout = self.start_engine()

        board = chess.Board()
        game = chess.pgn.Game()
        node = game
        human_white = self.human_as_white.get()

        while not board.is_game_over():
            print("\n" + str(board))
            print("FEN:", board.fen())

            if board.turn == chess.WHITE and human_white or board.turn == chess.BLACK and not human_white:
                move_input = input("Your move (UCI): ").strip()
                try:
                    move = chess.Move.from_uci(move_input)
                    if move in board.legal_moves:
                        board.push(move)
                        node = node.add_variation(move)
                    else:
                        print("Illegal move.")
                except:
                    print("Invalid UCI move.")
            elif board.turn == chess.WHITE if not human_white else False:
                move = self.get_nnue_move(board, model)
                board.push(move)
                node = node.add_variation(move)
            else:
                move = self.get_stockfish_move(board, send, stdout)
                board.push(move)
                node = node.add_variation(move)

        engine.terminate()
        self.save_game(game, "nnue_vs_human")

    def load_model(self):
        model = ImprovedNNUE()
        state = torch.load(self.model_path.get(), map_location=torch.device('cpu'))
        model.load_state_dict(state['model_state_dict'])
        model.eval()
        return model

    def save_game(self, game, prefix="game"):
        ts = datetime.now().strftime("%Y%m%d_%H%M%S")
        pgn_path = filedialog.asksaveasfilename(defaultextension=".pgn", initialfile=f"{prefix}_{ts}.pgn", filetypes=[("PGN File", "*.pgn")])
        if pgn_path:
            with open(pgn_path, "w") as f:
                print(game, file=f)
            messagebox.showinfo("Game Saved", f"PGN saved to:\n{pgn_path}")
        else:
            messagebox.showwarning("Cancelled", "Game not saved.")

    def validate_paths(self):
        if not self.model_path.get() or not self.stockfish_path.get():
            messagebox.showerror("Error", "Model and engine paths must be selected.")
            return False
        return True

if __name__ == "__main__":
    root = tk.Tk()
    App(root)
    root.mainloop()