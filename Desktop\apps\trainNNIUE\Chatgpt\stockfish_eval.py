import chess
import chess.engine
import chess.pgn
import datetime
import torch

class GameManager:
    def __init__(self, model, encoder, stockfish_path=None, depth=6):
        self.model = model
        self.encoder = encoder
        self.stockfish_path = stockfish_path
        self.depth = depth

    def board_to_input(self, board):
        from data_utils import board_to_tensor
        tensor = board_to_tensor(board)
        return tensor.reshape(1, 15, 8, 8)

    def model_move(self, board):
        legal_moves = list(board.legal_moves)
        input_tensor = self.board_to_input(board)
        with torch.no_grad():
            preds = self.model(torch.tensor(input_tensor))
            preds = preds.squeeze().numpy()

        scored_moves = []
        for move in legal_moves:
            idx = self.encoder.move2idx.get(move.uci())
            if idx is not None and idx < len(preds):
                scored_moves.append((preds[idx], move))
        if not scored_moves:
            return legal_moves[0]
        return max(scored_moves, key=lambda x: x[0])[1]

    def stockfish_move(self, board):
        with chess.engine.SimpleEngine.popen_uci(self.stockfish_path) as engine:
            result = engine.play(board, chess.engine.Limit(depth=self.depth))
            return result.move

    def play_vs_stockfish(self, save_path="vs_stockfish.pgn"):
        board = chess.Board()
        game = chess.pgn.Game()
        node = game
        while not board.is_game_over():
            move = self.model_move(board) if board.turn else self.stockfish_move(board)
            board.push(move)
            node = node.add_variation(move)
        game.headers["Event"] = "Model vs Stockfish"
        game.headers["Date"] = datetime.datetime.now().strftime("%Y.%m.%d")
        with open(save_path, "w") as f:
            print(game, file=f)
        return game.headers["Result"]

    def self_play(self, save_path="selfplay.pgn"):
        board = chess.Board()
        game = chess.pgn.Game()
        node = game
        while not board.is_game_over():
            move = self.model_move(board)
            board.push(move)
            node = node.add_variation(move)
        game.headers["Event"] = "Self-play"
        game.headers["Date"] = datetime.datetime.now().strftime("%Y.%m.%d")
        with open(save_path, "w") as f:
            print(game, file=f)
        return game.headers["Result"]