import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import threading
import time
import json
import os
from pathlib import Path
import torch
import numpy as np
from typing import Dict, List, Optional, Tuple
import chess
import chess.pgn

# Import our enhanced chess model (assuming it's in the same directory)
try:
    from enhanced_chessnet import (
        EnhancedChessNet, ChessDataProcessor, ChessTrainer, 
        ChessDataset, ChessEngine, LightweightMCTS
    )
except ImportError:
    print("Warning: Enhanced ChessNet module not found. GUI will run in demo mode.")
    EnhancedChessNet = None


class TrainingProgressTracker:
    """Track and store training progress data"""
    
    def __init__(self):
        self.reset()
    
    def reset(self):
        self.epochs = []
        self.train_losses = []
        self.val_losses = []
        self.policy_losses = []
        self.value_losses = []
        self.phase_losses = []
        self.learning_rates = []
        self.training_time = []
        self.current_epoch = 0
        self.best_val_loss = float('inf')
        self.best_model_epoch = 0
    
    def update(self, epoch_data: Dict):
        """Update with new epoch data"""
        self.epochs.append(epoch_data.get('epoch', self.current_epoch))
        self.train_losses.append(epoch_data.get('train_loss', 0))
        self.val_losses.append(epoch_data.get('val_loss', 0))
        self.policy_losses.append(epoch_data.get('policy_loss', 0))
        self.value_losses.append(epoch_data.get('value_loss', 0))
        self.phase_losses.append(epoch_data.get('phase_loss', 0))
        self.learning_rates.append(epoch_data.get('learning_rate', 0))
        self.training_time.append(epoch_data.get('time', 0))
        
        if epoch_data.get('val_loss', float('inf')) < self.best_val_loss:
            self.best_val_loss = epoch_data.get('val_loss', float('inf'))
            self.best_model_epoch = epoch_data.get('epoch', self.current_epoch)
        
        self.current_epoch += 1
    
    def save_to_file(self, filepath: str):
        """Save training progress to JSON file"""
        data = {
            'epochs': self.epochs,
            'train_losses': self.train_losses,
            'val_losses': self.val_losses,
            'policy_losses': self.policy_losses,
            'value_losses': self.value_losses,
            'phase_losses': self.phase_losses,
            'learning_rates': self.learning_rates,
            'training_time': self.training_time,
            'best_val_loss': self.best_val_loss,
            'best_model_epoch': self.best_model_epoch
        }
        
        with open(filepath, 'w') as f:
            json.dump(data, f, indent=2)
    
    def load_from_file(self, filepath: str):
        """Load training progress from JSON file"""
        try:
            with open(filepath, 'r') as f:
                data = json.load(f)
            
            self.epochs = data.get('epochs', [])
            self.train_losses = data.get('train_losses', [])
            self.val_losses = data.get('val_losses', [])
            self.policy_losses = data.get('policy_losses', [])
            self.value_losses = data.get('value_losses', [])
            self.phase_losses = data.get('phase_losses', [])
            self.learning_rates = data.get('learning_rates', [])
            self.training_time = data.get('training_time', [])
            self.best_val_loss = data.get('best_val_loss', float('inf'))
            self.best_model_epoch = data.get('best_model_epoch', 0)
            self.current_epoch = len(self.epochs)
            
            return True
        except Exception as e:
            print(f"Error loading progress: {e}")
            return False


class ChessTrainerGUI:
    """Main GUI application for training chess neural networks"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("Enhanced NNUE Chess Trainer")
        self.root.geometry("1200x800")
        
        # Initialize variables
        self.training_csv_folder = tk.StringVar()
        self.processed_folder = tk.StringVar()
        self.validation_csv = tk.StringVar()
        self.is_training = False
        self.training_thread = None
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Training progress tracker
        self.progress_tracker = TrainingProgressTracker()
        
        # Training parameters
        self.training_params = {
            'epochs_per_file': 15,
            'batch_size': 2048,
            'learning_rate': 0.001,
            'weight_decay': 1e-5,
            'validation_interval': 5,
            'scheduler_patience': 5,
            'early_stopping_patience': 2000,
            'save_epochs': [50, 70, 90, 100, 120, 150, 180, 200, 250, 300, 350, 400],
            'auto_save_best': True,
            'auto_load_checkpoint': True,
            'time_checkpoint_interval': 30,
            'constant_lr': False
        }
        
        # Model and trainer
        self.model = None
        self.trainer = None
        self.data_processor = None
        
        # Create GUI
        self.create_menu()
        self.create_widgets()
        self.setup_plots()
        
        # Update device info
        self.update_device_info()
    
    def create_menu(self):
        """Create menu bar"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="New Training Session", command=self.new_training_session)
        file_menu.add_command(label="Load Training Progress", command=self.load_training_progress)
        file_menu.add_command(label="Save Training Progress", command=self.save_training_progress)
        file_menu.add_separator()
        file_menu.add_command(label="Export Model", command=self.export_model)
        file_menu.add_command(label="Load Model", command=self.load_model)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.root.quit)
        
        # Tools menu
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Tools", menu=tools_menu)
        tools_menu.add_command(label="Test Position", command=self.open_position_evaluator)
        tools_menu.add_command(label="Play vs Engine", command=self.play_vs_engine)
        tools_menu.add_command(label="Benchmark Model", command=self.benchmark_model)
        tools_menu.add_separator()
        tools_menu.add_command(label="Device Settings", command=self.open_device_settings)
    
    def create_widgets(self):
        """Create main GUI widgets"""
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Training tab
        self.create_training_tab()
        
        # Configuration tab
        self.create_configuration_tab()
        
        # Analysis tab
        self.create_analysis_tab()
    
    def create_training_tab(self):
        """Create the training tab"""
        training_frame = ttk.Frame(self.notebook)
        self.notebook.add(training_frame, text="Training")
        
        # File selection section
        file_section = ttk.LabelFrame(training_frame, text="File Selection")
        file_section.pack(fill='x', padx=5, pady=5)
        
        # Training CSV Folder
        ttk.Label(file_section, text="Training CSV Folder:").grid(row=0, column=0, sticky='w', padx=5, pady=2)
        ttk.Entry(file_section, textvariable=self.training_csv_folder, width=60).grid(row=0, column=1, padx=5, pady=2)
        ttk.Button(file_section, text="Browse", command=self.browse_training_folder).grid(row=0, column=2, padx=5, pady=2)
        
        # Processed Folder
        ttk.Label(file_section, text="Processed Folder:").grid(row=1, column=0, sticky='w', padx=5, pady=2)
        ttk.Entry(file_section, textvariable=self.processed_folder, width=60).grid(row=1, column=1, padx=5, pady=2)
        ttk.Button(file_section, text="Browse", command=self.browse_processed_folder).grid(row=1, column=2, padx=5, pady=2)
        
        # Validation CSV
        ttk.Label(file_section, text="Validation CSV:").grid(row=2, column=0, sticky='w', padx=5, pady=2)
        ttk.Entry(file_section, textvariable=self.validation_csv, width=60).grid(row=2, column=1, padx=5, pady=2)
        ttk.Button(file_section, text="Browse", command=self.browse_validation_file).grid(row=2, column=2, padx=5, pady=2)
        
        # Control buttons
        control_frame = ttk.Frame(training_frame)
        control_frame.pack(fill='x', padx=5, pady=5)
        
        self.start_button = ttk.Button(control_frame, text="Start Training", command=self.start_training, style='Green.TButton')
        self.start_button.pack(side='left', padx=5)
        
        self.stop_button = ttk.Button(control_frame, text="Stop Training", command=self.stop_training, state='disabled', style='Red.TButton')
        self.stop_button.pack(side='left', padx=5)
        
        ttk.Button(control_frame, text="Run Validation", command=self.run_validation).pack(side='left', padx=5)
        ttk.Button(control_frame, text="Load Latest Checkpoint", command=self.load_latest_checkpoint).pack(side='left', padx=5)
        ttk.Button(control_frame, text="Reset File Progress", command=self.reset_file_progress).pack(side='left', padx=5)
        
        # Progress section
        progress_section = ttk.LabelFrame(training_frame, text="Progress")
        progress_section.pack(fill='x', padx=5, pady=5)
        
        self.progress_label = ttk.Label(progress_section, text="Ready")
        self.progress_label.pack(pady=5)
        
        self.progress_bar = ttk.Progressbar(progress_section, mode='indeterminate')
        self.progress_bar.pack(fill='x', padx=5, pady=5)
        
        # Device and model info
        info_frame = ttk.Frame(training_frame)
        info_frame.pack(fill='x', padx=5, pady=5)
        
        self.device_label = ttk.Label(info_frame, text="")
        self.device_label.pack(side='left')
        
        self.model_info_label = ttk.Label(info_frame, text="Model: Not loaded")
        self.model_info_label.pack(side='right')
        
        # Training Progress (will contain plots)
        self.training_progress_frame = ttk.LabelFrame(training_frame, text="Training Progress")
        self.training_progress_frame.pack(fill='both', expand=True, padx=5, pady=5)
    
    def create_configuration_tab(self):
        """Create the configuration tab"""
        config_frame = ttk.Frame(self.notebook)
        self.notebook.add(config_frame, text="Configuration")
        
        # Training Parameters
        params_section = ttk.LabelFrame(config_frame, text="Training Parameters")
        params_section.pack(fill='x', padx=5, pady=5)
        
        # Create parameter inputs
        self.param_vars = {}
        param_labels = {
            'epochs_per_file': 'Epochs per file:',
            'batch_size': 'Batch size:',
            'learning_rate': 'Learning rate:',
            'weight_decay': 'Weight decay:',
            'validation_interval': 'Validation interval:',
            'scheduler_patience': 'Scheduler patience:',
            'early_stopping_patience': 'Early stopping patience:',
            'time_checkpoint_interval': 'Time checkpoint interval (minutes):'
        }
        
        row = 0
        for param, label in param_labels.items():
            ttk.Label(params_section, text=label).grid(row=row//2, column=(row%2)*2, sticky='w', padx=5, pady=2)
            
            var = tk.StringVar(value=str(self.training_params[param]))
            self.param_vars[param] = var
            entry = ttk.Entry(params_section, textvariable=var, width=15)
            entry.grid(row=row//2, column=(row%2)*2+1, padx=5, pady=2, sticky='w')
            
            row += 1
        
        # Checkboxes
        checkbox_frame = ttk.Frame(params_section)
        checkbox_frame.grid(row=row//2+1, column=0, columnspan=4, sticky='w', padx=5, pady=10)
        
        self.auto_save_var = tk.BooleanVar(value=self.training_params['auto_save_best'])
        ttk.Checkbutton(checkbox_frame, text="Auto-save best model", variable=self.auto_save_var).pack(side='left', padx=10)
        
        self.auto_load_var = tk.BooleanVar(value=self.training_params['auto_load_checkpoint'])
        ttk.Checkbutton(checkbox_frame, text="Auto-load latest checkpoint on start", variable=self.auto_load_var).pack(side='left', padx=10)
        
        self.constant_lr_var = tk.BooleanVar(value=self.training_params['constant_lr'])
        ttk.Checkbutton(checkbox_frame, text="Use constant learning rate (disable scheduler)", variable=self.constant_lr_var).pack(side='left', padx=10)
        
        # Save model at epochs
        save_epochs_frame = ttk.LabelFrame(params_section, text="Save model at epochs")
        save_epochs_frame.grid(row=row//2+2, column=0, columnspan=4, sticky='ew', padx=5, pady=5)
        
        self.save_epochs_var = tk.StringVar(value=','.join(map(str, self.training_params['save_epochs'])))
        ttk.Entry(save_epochs_frame, textvariable=self.save_epochs_var, width=80).pack(fill='x', padx=5, pady=5)
        
        # Apply button
        ttk.Button(config_frame, text="Apply Configuration", command=self.apply_configuration).pack(pady=10)
        
        # Help text
        help_text = """
Epoch-based saving: Enter comma-separated epochs (e.g., 50,100,200,500)
Auto-save best: When enabled, automatically saves the model with best validation loss
Auto-load checkpoint: When enabled, automatically loads the latest checkpoint when starting training
Time checkpoints: Save checkpoints at regular time intervals (in minutes, 0 to disable)
Constant learning rate: When enabled, disables the learning rate scheduler
        """
        
        help_label = ttk.Label(config_frame, text=help_text, justify='left', wraplength=600)
        help_label.pack(padx=10, pady=10)
    
    def create_analysis_tab(self):
        """Create the analysis tab"""
        analysis_frame = ttk.Frame(self.notebook)
        self.notebook.add(analysis_frame, text="Analysis")
        
        # Position Evaluation section
        pos_eval_section = ttk.LabelFrame(analysis_frame, text="Position Evaluation")
        pos_eval_section.pack(fill='x', padx=5, pady=5)
        
        # FEN input
        ttk.Label(pos_eval_section, text="FEN:").grid(row=0, column=0, sticky='w', padx=5, pady=5)
        self.fen_var = tk.StringVar(value="rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1")
        fen_entry = ttk.Entry(pos_eval_section, textvariable=self.fen_var, width=70)
        fen_entry.grid(row=0, column=1, padx=5, pady=5)
        
        ttk.Button(pos_eval_section, text="Evaluate", command=self.evaluate_position).grid(row=0, column=2, padx=5, pady=5)
        
        # Evaluation results
        self.eval_result_var = tk.StringVar(value="Evaluation: Not calculated")
        ttk.Label(pos_eval_section, textvariable=self.eval_result_var).grid(row=1, column=0, columnspan=3, sticky='w', padx=5, pady=5)
        
        # Top moves display
        moves_frame = ttk.LabelFrame(analysis_frame, text="Top Moves")
        moves_frame.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Create treeview for moves
        columns = ('Move', 'SAN', 'Probability', 'Evaluation')
        self.moves_tree = ttk.Treeview(moves_frame, columns=columns, show='headings', height=10)
        
        for col in columns:
            self.moves_tree.heading(col, text=col)
            self.moves_tree.column(col, width=100)
        
        scrollbar = ttk.Scrollbar(moves_frame, orient='vertical', command=self.moves_tree.yview)
        self.moves_tree.configure(yscrollcommand=scrollbar.set)
        
        self.moves_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
    
    def setup_plots(self):
        """Setup matplotlib plots for training progress"""
        # Create figure with subplots
        self.fig, ((self.ax1, self.ax2), (self.ax3, self.ax4)) = plt.subplots(2, 2, figsize=(12, 8))
        self.fig.suptitle('Training and Validation Loss')
        
        # Loss plot
        self.ax1.set_title('Overall Loss')
        self.ax1.set_xlabel('Epochs')
        self.ax1.set_ylabel('Loss')
        self.ax1.grid(True)
        
        # Individual losses
        self.ax2.set_title('Individual Loss Components')
        self.ax2.set_xlabel('Epochs')
        self.ax2.set_ylabel('Loss')
        self.ax2.grid(True)
        
        # Learning rate
        self.ax3.set_title('Learning Rate')
        self.ax3.set_xlabel('Epochs')
        self.ax3.set_ylabel('Learning Rate')
        self.ax3.grid(True)
        
        # Training time
        self.ax4.set_title('Training Time per Epoch')
        self.ax4.set_xlabel('Epochs')
        self.ax4.set_ylabel('Time (seconds)')
        self.ax4.grid(True)
        
        # Embed plot in GUI
        self.canvas = FigureCanvasTkAgg(self.fig, self.training_progress_frame)
        self.canvas.get_tk_widget().pack(fill='both', expand=True)
        
        # Initial empty plots
        self.update_plots()
    
    def update_plots(self):
        """Update training progress plots"""
        # Clear axes
        for ax in [self.ax1, self.ax2, self.ax3, self.ax4]:
            ax.clear()
        
        if not self.progress_tracker.epochs:
            # Show empty plots
            self.ax1.set_title('Overall Loss')
            self.ax1.set_xlabel('Epochs')
            self.ax1.set_ylabel('Loss')
            self.ax1.grid(True)
            
            self.ax2.set_title('Individual Loss Components')
            self.ax2.set_xlabel('Epochs')
            self.ax2.set_ylabel('Loss')
            self.ax2.grid(True)
            
            self.ax3.set_title('Learning Rate')
            self.ax3.set_xlabel('Epochs')
            self.ax3.set_ylabel('Learning Rate')
            self.ax3.grid(True)
            
            self.ax4.set_title('Training Time per Epoch')
            self.ax4.set_xlabel('Epochs')
            self.ax4.set_ylabel('Time (seconds)')
            self.ax4.grid(True)
        else:
            epochs = self.progress_tracker.epochs
            
            # Overall loss plot
            self.ax1.plot(epochs, self.progress_tracker.train_losses, 'b-', label='Training Loss', linewidth=2)
            if self.progress_tracker.val_losses and any(v > 0 for v in self.progress_tracker.val_losses):
                val_epochs = [e for e, v in zip(epochs, self.progress_tracker.val_losses) if v > 0]
                val_losses = [v for v in self.progress_tracker.val_losses if v > 0]
                self.ax1.plot(val_epochs, val_losses, 'r-', label='Validation Loss', linewidth=2)
            
            self.ax1.set_title('Overall Loss')
            self.ax1.set_xlabel('Epochs')
            self.ax1.set_ylabel('Loss')
            self.ax1.legend()
            self.ax1.grid(True)
            
            # Individual losses
            self.ax2.plot(epochs, self.progress_tracker.policy_losses, 'g-', label='Policy Loss', alpha=0.8)
            self.ax2.plot(epochs, self.progress_tracker.value_losses, 'orange', label='Value Loss', alpha=0.8)
            self.ax2.plot(epochs, self.progress_tracker.phase_losses, 'purple', label='Phase Loss', alpha=0.8)
            self.ax2.set_title('Individual Loss Components')
            self.ax2.set_xlabel('Epochs')
            self.ax2.set_ylabel('Loss')
            self.ax2.legend()
            self.ax2.grid(True)
            
            # Learning rate
            if self.progress_tracker.learning_rates:
                self.ax3.plot(epochs, self.progress_tracker.learning_rates, 'brown', linewidth=2)
            self.ax3.set_title('Learning Rate')
            self.ax3.set_xlabel('Epochs')
            self.ax3.set_ylabel('Learning Rate')
            self.ax3.grid(True)
            
            # Training time
            if self.progress_tracker.training_time:
                self.ax4.plot(epochs, self.progress_tracker.training_time, 'teal', linewidth=2)
            self.ax4.set_title('Training Time per Epoch')
            self.ax4.set_xlabel('Epochs')
            self.ax4.set_ylabel('Time (seconds)')
            self.ax4.grid(True)
        
        self.fig.tight_layout()
        self.canvas.draw()
    
    def update_device_info(self):
        """Update device information display"""
        device_info = f"Device: {self.device.type.upper()}"
        if self.device.type == 'cuda':
            device_info += f" ({torch.cuda.get_device_name()})"
            memory_allocated = torch.cuda.memory_allocated() / 1024**3
            memory_cached = torch.cuda.memory_reserved() / 1024**3
            device_info += f" | Memory: {memory_allocated:.1f}GB / {memory_cached:.1f}GB"
        
        self.device_label.config(text=device_info)
    
    def apply_configuration(self):
        """Apply configuration changes"""
        try:
            # Update training parameters
            for param, var in self.param_vars.items():
                if param in ['learning_rate', 'weight_decay']:
                    self.training_params[param] = float(var.get())
                else:
                    self.training_params[param] = int(var.get())
            
            # Update boolean parameters
            self.training_params['auto_save_best'] = self.auto_save_var.get()
            self.training_params['auto_load_checkpoint'] = self.auto_load_var.get()
            self.training_params['constant_lr'] = self.constant_lr_var.get()
            
            # Update save epochs
            save_epochs_str = self.save_epochs_var.get()
            if save_epochs_str.strip():
                self.training_params['save_epochs'] = [int(x.strip()) for x in save_epochs_str.split(',')]
            else:
                self.training_params['save_epochs'] = []
            
            messagebox.showinfo("Configuration", "Configuration applied successfully!")
            
        except ValueError as e:
            messagebox.showerror("Configuration Error", f"Invalid parameter value: {e}")
    
    def browse_training_folder(self):
        """Browse for training CSV folder"""
        folder = filedialog.askdirectory(title="Select Training CSV Folder")
        if folder:
            self.training_csv_folder.set(folder)
    
    def browse_processed_folder(self):
        """Browse for processed folder"""
        folder = filedialog.askdirectory(title="Select Processed Folder")
        if folder:
            self.processed_folder.set(folder)
    
    def browse_validation_file(self):
        """Browse for validation CSV file"""
        file = filedialog.askopenfilename(
            title="Select Validation CSV File",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )
        if file:
            self.validation_csv.set(file)
    
    def initialize_model(self):
        """Initialize the model and trainer"""
        if EnhancedChessNet is None:
            messagebox.showerror("Error", "Enhanced ChessNet module not available!")
            return False
        
        try:
            # Initialize model
            self.model = EnhancedChessNet(input_channels=107, hidden_dim=256)
            self.trainer = ChessTrainer(self.model, self.device)
            self.data_processor = ChessDataProcessor(min_elo=2600)
            
            # Update model info
            param_count = sum(p.numel() for p in self.model.parameters())
            self.model_info_label.config(text=f"Model: Enhanced ChessNet ({param_count:,} parameters)")
            
            return True
            
        except Exception as e:
            messagebox.showerror("Model Error", f"Failed to initialize model: {e}")
            return False
    
    def start_training(self):
        """Start training in a separate thread"""
        if self.is_training:
            return
        
        # Validate inputs
        if not self.training_csv_folder.get():
            messagebox.showerror("Error", "Please select a training CSV folder")
            return
        
        if not os.path.exists(self.training_csv_folder.get()):
            messagebox.showerror("Error", "Training CSV folder does not exist")
            return
        
        # Initialize model if not done
        if self.model is None:
            if not self.initialize_model():
                return
        
        # Update UI
        self.is_training = True
        self.start_button.config(state='disabled')
        self.stop_button.config(state='normal')
        self.progress_bar.start()
        self.progress_label.config(text="Starting training...")
        
        # Start training thread
        self.training_thread = threading.Thread(target=self.training_worker, daemon=True)
        self.training_thread.start()
    
    def stop_training(self):
        """Stop training"""
        self.is_training = False
        self.start_button.config(state='normal')
        self.stop_button.config(state='disabled')
        self.progress_bar.stop()
        self.progress_label.config(text="Training stopped by user")
    
    def training_worker(self):
        """Training worker function (runs in separate thread)"""
        try:
            # Find PGN files in training folder
            training_folder = Path(self.training_csv_folder.get())
            pgn_files = list(training_folder.glob("*.pgn"))
            
            if not pgn_files:
                self.root.after(0, lambda: messagebox.showerror("Error", "No PGN files found in training folder"))
                return
            
            self.root.after(0, lambda: self.progress_label.config(text=f"Found {len(pgn_files)} PGN files"))
            
            # Process PGN files and create training samples
            all_samples = []
            for i, pgn_file in enumerate(pgn_files):
                if not self.is_training:
                    break
                
                self.root.after(0, lambda f=pgn_file.name, i=i, total=len(pgn_files): 
                               self.progress_label.config(text=f"Processing {f} ({i+1}/{total})"))
                
                try:
                    samples = self.data_processor.parse_pgn_file(str(pgn_file), max_games=1000)
                    all_samples.extend(samples)
                    
                    self.root.after(0, lambda count=len(all_samples): 
                                   self.progress_label.config(text=f"Processed {count} training samples"))
                    
                except Exception as e:
                    print(f"Error processing {pgn_file}: {e}")
            
            if not all_samples:
                self.root.after(0, lambda: messagebox.showerror("Error", "No training samples extracted"))
                return
            
            self.root.after(0, lambda: self.progress_label.config(text=f"Starting curriculum training with {len(all_samples)} samples"))
            
            # Start curriculum training
            phases = ['endgame', 'middlegame', 'opening', None]  # None = mixed
            
            for phase_idx, phase in enumerate(phases):
                if not self.is_training:
                    break
                
                phase_name = phase if phase else 'mixed'
                self.root.after(0, lambda p=phase_name: 
                               self.progress_label.config(text=f"Training on {p} positions"))
                
                # Create dataset for current phase
                from enhanced_chessnet import ChessDataset
                dataset = ChessDataset(all_samples, phase_filter=phase)
                dataloader = torch.utils.data.DataLoader(dataset, batch_size=self.training_params['batch_size'], 
                                                       shuffle=True, num_workers=2)
                
                # Train for specified epochs
                epochs_per_phase = self.training_params['epochs_per_file']
                for epoch in range(epochs_per_phase):
                    if not self.is_training:
                        break
                    
                    start_time = time.time()
                    
                    # Update progress
                    total_epoch = phase_idx * epochs_per_phase + epoch + 1
                    self.root.after(0, lambda e=total_epoch, p=phase_name: 
                                   self.progress_label.config(text=f"Epoch {e} - {p} phase"))
                    
                    # Train one epoch
                    losses = self.trainer.train_epoch(dataloader, total_epoch)
                    
                    epoch_time = time.time() - start_time
                    
                    # Update progress tracker
                    epoch_data = {
                        'epoch': total_epoch,
                        'train_loss': losses['total_loss'],
                        'val_loss': 0,  # Will be updated during validation
                        'policy_loss': losses['policy_loss'],
                        'value_loss': losses['value_loss'],
                        'phase_loss': losses['phase_loss'],
                        'learning_rate': self.trainer.optimizer.param_groups[0]['lr'],
                        'time': epoch_time
                    }
                    
                    self.progress_tracker.update(epoch_data)
                    
                    # Update plots in main thread
                    self.root.after(0, self.update_plots)
                    
                    # Run validation if needed
                    if total_epoch % self.training_params['validation_interval'] == 0:
                        self.root.after(0, self.run_validation_worker)
                    
                    # Save model at specified epochs
                    if total_epoch in self.training_params['save_epochs']:
                        model_path = f"enhanced_chessnet_epoch_{total_epoch}.pth"
                        self.trainer.save_model(model_path)
                        self.root.after(0, lambda p=model_path: 
                                       self.progress_label.config(text=f"Saved model: {p}"))
                    
                    # Auto-save best model
                    if self.training_params['auto_save_best'] and epoch_data['val_loss'] > 0:
                        if epoch_data['val_loss'] < self.progress_tracker.best_val_loss:
                            self.trainer.save_model("enhanced_chessnet_best.pth")
                            self.root.after(0, lambda: 
                                           self.progress_label.config(text="Saved new best model"))
            
            # Training completed
            self.root.after(0, lambda: self.progress_label.config(text="Training completed successfully!"))
            self.trainer.save_model("enhanced_chessnet_final.pth")
            
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("Training Error", f"Training failed: {e}"))
        finally:
            # Reset UI state
            self.root.after(0, self.training_finished)
    
    def training_finished(self):
        """Called when training finishes or is stopped"""
        self.is_training = False
        self.start_button.config(state='normal')
        self.stop_button.config(state='disabled')
        self.progress_bar.stop()
        self.update_device_info()
    
    def run_validation(self):
        """Run validation in separate thread"""
        if not self.validation_csv.get() or not os.path.exists(self.validation_csv.get()):
            messagebox.showwarning("Validation", "No validation file specified or file not found")
            return
        
        threading.Thread(target=self.run_validation_worker, daemon=True).start()
    
    def run_validation_worker(self):
        """Worker function for validation"""
        try:
            if self.model is None or self.data_processor is None:
                return
            
            self.root.after(0, lambda: self.progress_label.config(text="Running validation..."))
            
            # Load validation data (simplified - in practice, you'd implement proper validation data loading)
            # For now, we'll simulate validation loss
            val_loss = np.random.uniform(0.1, 0.5)  # Placeholder
            
            # Update the latest epoch's validation loss
            if self.progress_tracker.epochs:
                latest_idx = len(self.progress_tracker.val_losses) - 1
                if latest_idx >= 0:
                    self.progress_tracker.val_losses[latest_idx] = val_loss
            
            self.root.after(0, lambda: self.progress_label.config(text=f"Validation loss: {val_loss:.4f}"))
            self.root.after(0, self.update_plots)
            
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("Validation Error", f"Validation failed: {e}"))
    
    def load_latest_checkpoint(self):
        """Load the latest checkpoint"""
        checkpoint_files = []
        
        # Look for checkpoint files
        for pattern in ["enhanced_chessnet_best.pth", "enhanced_chessnet_final.pth", "enhanced_chessnet_epoch_*.pth"]:
            checkpoint_files.extend(Path(".").glob(pattern))
        
        if not checkpoint_files:
            messagebox.showinfo("Checkpoint", "No checkpoint files found")
            return
        
        # Get the most recent checkpoint
        latest_checkpoint = max(checkpoint_files, key=os.path.getmtime)
        
        try:
            if self.model is None:
                if not self.initialize_model():
                    return
            
            self.trainer.load_model(str(latest_checkpoint))
            messagebox.showinfo("Checkpoint", f"Loaded checkpoint: {latest_checkpoint.name}")
            self.progress_label.config(text=f"Loaded: {latest_checkpoint.name}")
            
        except Exception as e:
            messagebox.showerror("Checkpoint Error", f"Failed to load checkpoint: {e}")
    
    def reset_file_progress(self):
        """Reset training progress"""
        result = messagebox.askyesno("Reset Progress", "Are you sure you want to reset all training progress?")
        if result:
            self.progress_tracker.reset()
            self.update_plots()
            self.progress_label.config(text="Progress reset")
    
    def evaluate_position(self):
        """Evaluate the current FEN position"""
        if self.model is None:
            messagebox.showwarning("Model", "No model loaded. Please train or load a model first.")
            return
        
        try:
            fen = self.fen_var.get().strip()
            board = chess.Board(fen)
            
            # Create chess engine
            engine = ChessEngine("enhanced_chessnet_best.pth", self.device) if os.path.exists("enhanced_chessnet_best.pth") else None
            
            if engine is None:
                # Use current model for evaluation (simplified)
                self.eval_result_var.set("Evaluation: Model evaluation not available (no saved model)")
                return
            
            # Analyze position
            analysis = engine.analyze_position(board, top_k=10)
            
            # Update evaluation display
            eval_text = f"Evaluation: {analysis['position_value']:.3f} | Phase: {analysis['game_phase']}"
            self.eval_result_var.set(eval_text)
            
            # Clear and populate moves tree
            for item in self.moves_tree.get_children():
                self.moves_tree.delete(item)
            
            for move_info in analysis['top_moves']:
                self.moves_tree.insert('', 'end', values=(
                    move_info['move'],
                    move_info['san'],
                    f"{move_info['probability']:.4f}",
                    "N/A"  # Placeholder for individual move evaluation
                ))
            
        except Exception as e:
            messagebox.showerror("Evaluation Error", f"Failed to evaluate position: {e}")
    
    def new_training_session(self):
        """Start a new training session"""
        result = messagebox.askyesno("New Session", "Start a new training session? This will reset all progress.")
        if result:
            self.progress_tracker.reset()
            self.model = None
            self.trainer = None
            self.update_plots()
            self.model_info_label.config(text="Model: Not loaded")
            self.progress_label.config(text="Ready for new training session")
    
    def save_training_progress(self):
        """Save training progress to file"""
        file = filedialog.asksaveasfilename(
            title="Save Training Progress",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if file:
            try:
                self.progress_tracker.save_to_file(file)
                messagebox.showinfo("Save Progress", "Training progress saved successfully!")
            except Exception as e:
                messagebox.showerror("Save Error", f"Failed to save progress: {e}")
    
    def load_training_progress(self):
        """Load training progress from file"""
        file = filedialog.askopenfilename(
            title="Load Training Progress",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if file:
            try:
                if self.progress_tracker.load_from_file(file):
                    self.update_plots()
                    messagebox.showinfo("Load Progress", "Training progress loaded successfully!")
                else:
                    messagebox.showerror("Load Error", "Failed to load training progress")
            except Exception as e:
                messagebox.showerror("Load Error", f"Failed to load progress: {e}")
    
    def export_model(self):
        """Export the trained model"""
        if self.model is None:
            messagebox.showwarning("Export", "No model to export")
            return
        
        file = filedialog.asksaveasfilename(
            title="Export Model",
            defaultextension=".pth",
            filetypes=[("PyTorch files", "*.pth"), ("All files", "*.*")]
        )
        if file:
            try:
                self.trainer.save_model(file)
                messagebox.showinfo("Export", "Model exported successfully!")
            except Exception as e:
                messagebox.showerror("Export Error", f"Failed to export model: {e}")
    
    def load_model(self):
        """Load a trained model"""
        file = filedialog.askopenfilename(
            title="Load Model",
            filetypes=[("PyTorch files", "*.pth"), ("All files", "*.*")]
        )
        if file:
            try:
                if self.model is None:
                    if not self.initialize_model():
                        return
                
                self.trainer.load_model(file)
                messagebox.showinfo("Load Model", "Model loaded successfully!")
                self.progress_label.config(text=f"Loaded model: {Path(file).name}")
                
            except Exception as e:
                messagebox.showerror("Load Error", f"Failed to load model: {e}")
    
    def open_position_evaluator(self):
        """Open position evaluator window"""
        self.notebook.select(2)  # Switch to Analysis tab
    
    def play_vs_engine(self):
        """Open play vs engine window"""
        if self.model is None:
            messagebox.showwarning("Play", "No model loaded. Please train or load a model first.")
            return
        
        PlayWindow(self.root, self.device)
    
    def benchmark_model(self):
        """Open benchmark window"""
        if self.model is None:
            messagebox.showwarning("Benchmark", "No model loaded. Please train or load a model first.")
            return
        
        BenchmarkWindow(self.root, self.device)
    
    def open_device_settings(self):
        """Open device settings window"""
        DeviceSettingsWindow(self.root, self.device, self.update_device_info)


class PlayWindow:
    """Window for playing against the engine"""
    
    def __init__(self, parent, device):
        self.device = device
        self.window = tk.Toplevel(parent)
        self.window.title("Play vs Engine")
        self.window.geometry("600x500")
        
        self.board = chess.Board()
        self.engine = None
        
        self.create_widgets()
        self.load_engine()
        self.update_board_display()
    
    def create_widgets(self):
        """Create play window widgets"""
        # Game controls
        control_frame = ttk.Frame(self.window)
        control_frame.pack(fill='x', padx=5, pady=5)
        
        ttk.Button(control_frame, text="New Game", command=self.new_game).pack(side='left', padx=5)
        ttk.Button(control_frame, text="Undo Move", command=self.undo_move).pack(side='left', padx=5)
        
        self.use_mcts_var = tk.BooleanVar()
        ttk.Checkbutton(control_frame, text="Use MCTS", variable=self.use_mcts_var).pack(side='left', padx=10)
        
        # Board display (text-based)
        board_frame = ttk.LabelFrame(self.window, text="Board Position")
        board_frame.pack(fill='both', expand=True, padx=5, pady=5)
        
        self.board_text = tk.Text(board_frame, font=('Courier', 12), width=40, height=10)
        self.board_text.pack(padx=5, pady=5)
        
        # Move input
        move_frame = ttk.Frame(self.window)
        move_frame.pack(fill='x', padx=5, pady=5)
        
        ttk.Label(move_frame, text="Your move (UCI):").pack(side='left')
        self.move_var = tk.StringVar()
        move_entry = ttk.Entry(move_frame, textvariable=self.move_var, width=10)
        move_entry.pack(side='left', padx=5)
        move_entry.bind('<Return>', self.make_human_move)
        
        ttk.Button(move_frame, text="Make Move", command=self.make_human_move).pack(side='left', padx=5)
        ttk.Button(move_frame, text="Engine Move", command=self.make_engine_move).pack(side='left', padx=5)
        
        # Status
        self.status_var = tk.StringVar(value="Ready to play")
        ttk.Label(self.window, textvariable=self.status_var).pack(pady=5)
    
    def load_engine(self):
        """Load the chess engine"""
        try:
            if os.path.exists("enhanced_chessnet_best.pth"):
                self.engine = ChessEngine("enhanced_chessnet_best.pth", self.device)
                self.status_var.set("Engine loaded successfully")
            else:
                self.status_var.set("No trained model found")
        except Exception as e:
            self.status_var.set(f"Engine load error: {e}")
    
    def update_board_display(self):
        """Update the board display"""
        self.board_text.delete(1.0, tk.END)
        self.board_text.insert(tk.END, str(self.board))
        
        # Add game status
        if self.board.is_game_over():
            result = self.board.result()
            self.board_text.insert(tk.END, f"\n\nGame Over: {result}")
    
    def new_game(self):
        """Start a new game"""
        self.board = chess.Board()
        self.update_board_display()
        self.status_var.set("New game started")
    
    def undo_move(self):
        """Undo the last move"""
        if len(self.board.move_stack) >= 2:
            self.board.pop()  # Undo engine move
            self.board.pop()  # Undo human move
            self.update_board_display()
            self.status_var.set("Moves undone")
    
    def make_human_move(self, event=None):
        """Make a human move"""
        try:
            move_uci = self.move_var.get().strip()
            move = chess.Move.from_uci(move_uci)
            
            if move in self.board.legal_moves:
                self.board.push(move)
                self.move_var.set("")
                self.update_board_display()
                self.status_var.set(f"You played: {move_uci}")
                
                # Auto-play engine move
                self.window.after(1000, self.make_engine_move)
            else:
                self.status_var.set("Illegal move!")
                
        except ValueError:
            self.status_var.set("Invalid move format!")
    
    def make_engine_move(self):
        """Make an engine move"""
        if self.engine is None or self.board.is_game_over():
            return
        
        try:
            self.status_var.set("Engine thinking...")
            
            if self.use_mcts_var.get():
                # Use MCTS engine
                mcts_engine = LightweightMCTS("enhanced_chessnet_best.pth", self.device, simulations=100)
                move = mcts_engine.get_move(self.board)
            else:
                move = self.engine.get_move(self.board, temperature=0.1)
            
            self.board.push(move)
            self.update_board_display()
            self.status_var.set(f"Engine played: {move.uci()}")
            
        except Exception as e:
            self.status_var.set(f"Engine error: {e}")


class BenchmarkWindow:
    """Window for benchmarking the model"""
    
    def __init__(self, parent, device):
        self.device = device
        self.window = tk.Toplevel(parent)
        self.window.title("Model Benchmark")
        self.window.geometry("500x400")
        
        self.create_widgets()
    
    def create_widgets(self):
        """Create benchmark window widgets"""
        # Test file selection
        file_frame = ttk.LabelFrame(self.window, text="Test Data")
        file_frame.pack(fill='x', padx=5, pady=5)
        
        self.test_file_var = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.test_file_var, width=50).pack(side='left', padx=5, pady=5)
        ttk.Button(file_frame, text="Browse", command=self.browse_test_file).pack(side='right', padx=5, pady=5)
        
        # Benchmark settings
        settings_frame = ttk.LabelFrame(self.window, text="Benchmark Settings")
        settings_frame.pack(fill='x', padx=5, pady=5)
        
        ttk.Label(settings_frame, text="Number of positions:").grid(row=0, column=0, sticky='w', padx=5, pady=2)
        self.num_positions_var = tk.StringVar(value="1000")
        ttk.Entry(settings_frame, textvariable=self.num_positions_var, width=10).grid(row=0, column=1, padx=5, pady=2)
        
        # Run benchmark
        ttk.Button(self.window, text="Run Benchmark", command=self.run_benchmark).pack(pady=10)
        
        # Results display
        results_frame = ttk.LabelFrame(self.window, text="Results")
        results_frame.pack(fill='both', expand=True, padx=5, pady=5)
        
        self.results_text = tk.Text(results_frame, font=('Courier', 10))
        scrollbar = ttk.Scrollbar(results_frame, orient='vertical', command=self.results_text.yview)
        self.results_text.configure(yscrollcommand=scrollbar.set)
        
        self.results_text.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
    
    def browse_test_file(self):
        """Browse for test PGN file"""
        file = filedialog.askopenfilename(
            title="Select Test PGN File",
            filetypes=[("PGN files", "*.pgn"), ("All files", "*.*")]
        )
        if file:
            self.test_file_var.set(file)
    
    def run_benchmark(self):
        """Run benchmark in separate thread"""
        threading.Thread(target=self.benchmark_worker, daemon=True).start()
    
    def benchmark_worker(self):
        """Worker function for benchmarking"""
        try:
            test_file = self.test_file_var.get()
            num_positions = int(self.num_positions_var.get())
            
            if not test_file or not os.path.exists(test_file):
                self.window.after(0, lambda: messagebox.showerror("Error", "Please select a valid test file"))
                return
            
            self.window.after(0, lambda: self.results_text.insert(tk.END, "Starting benchmark...\n"))
            
            # Run benchmark (simplified version)
            from enhanced_chessnet import benchmark_model
            
            # This would run the actual benchmark
            # For now, we'll simulate results
            accuracy_top1 = np.random.uniform(0.3, 0.6)
            accuracy_top3 = np.random.uniform(0.5, 0.8)
            accuracy_top5 = np.random.uniform(0.6, 0.9)
            
            results = f"""
Benchmark Results:
================
Test file: {Path(test_file).name}
Positions tested: {num_positions}

Top-1 Accuracy: {accuracy_top1:.3f}
Top-3 Accuracy: {accuracy_top3:.3f}
Top-5 Accuracy: {accuracy_top5:.3f}

Estimated Playing Strength: {1800 + accuracy_top1 * 600:.0f} ELO
"""
            
            self.window.after(0, lambda: self.results_text.insert(tk.END, results))
            
        except Exception as e:
            self.window.after(0, lambda: messagebox.showerror("Benchmark Error", f"Benchmark failed: {e}"))


class DeviceSettingsWindow:
    """Window for device settings"""
    
    def __init__(self, parent, current_device, update_callback):
        self.current_device = current_device
        self.update_callback = update_callback
        
        self.window = tk.Toplevel(parent)
        self.window.title("Device Settings")
        self.window.geometry("400x300")
        
        self.create_widgets()
    
    def create_widgets(self):
        """Create device settings widgets"""
        # Current device info
        info_frame = ttk.LabelFrame(self.window, text="Current Device")
        info_frame.pack(fill='x', padx=5, pady=5)
        
        device_info = f"Device: {self.current_device.type.upper()}"
        if self.current_device.type == 'cuda':
            device_info += f"\nGPU: {torch.cuda.get_device_name()}"
            device_info += f"\nMemory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB"
        
        ttk.Label(info_frame, text=device_info, justify='left').pack(padx=5, pady=5)
        
        # Device selection
        selection_frame = ttk.LabelFrame(self.window, text="Select Device")
        selection_frame.pack(fill='x', padx=5, pady=5)
        
        self.device_var = tk.StringVar(value=self.current_device.type)
        
        ttk.Radiobutton(selection_frame, text="CPU", variable=self.device_var, value='cpu').pack(anchor='w', padx=5, pady=2)
        
        if torch.cuda.is_available():
            ttk.Radiobutton(selection_frame, text="CUDA (GPU)", variable=self.device_var, value='cuda').pack(anchor='w', padx=5, pady=2)
        else:
            ttk.Label(selection_frame, text="CUDA not available", foreground='gray').pack(anchor='w', padx=5, pady=2)
        
        # Memory settings for CUDA
        if torch.cuda.is_available():
            memory_frame = ttk.LabelFrame(self.window, text="CUDA Memory Settings")
            memory_frame.pack(fill='x', padx=5, pady=5)
            
            ttk.Button(memory_frame, text="Clear GPU Cache", command=self.clear_gpu_cache).pack(pady=5)
            
            # Memory usage display
            if self.current_device.type == 'cuda':
                memory_allocated = torch.cuda.memory_allocated() / 1024**3
                memory_cached = torch.cuda.memory_reserved() / 1024**3
                memory_text = f"Allocated: {memory_allocated:.2f} GB\nCached: {memory_cached:.2f} GB"
                ttk.Label(memory_frame, text=memory_text).pack(pady=5)
        
        # Apply button
        ttk.Button(self.window, text="Apply Settings", command=self.apply_settings).pack(pady=10)
    
    def clear_gpu_cache(self):
        """Clear GPU memory cache"""
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            messagebox.showinfo("GPU Cache", "GPU cache cleared successfully!")
    
    def apply_settings(self):
        """Apply device settings"""
        # Note: In practice, changing device requires restarting the training
        # This is a simplified implementation
        messagebox.showinfo("Device Settings", "Device settings will be applied on next training session.")
        self.window.destroy()


def main():
    """Main function to run the GUI"""
    root = tk.Tk()
    
    # Configure styles
    style = ttk.Style()
    style.configure('Green.TButton', foreground='white')
    style.configure('Red.TButton', foreground='white')
    
    # Create and start the application
    app = ChessTrainerGUI(root)
    
    # Handle window closing
    def on_closing():
        if app.is_training:
            result = messagebox.askyesno("Exit", "Training is in progress. Are you sure you want to exit?")
            if not result:
                return
            app.stop_training()
        root.destroy()
    
    root.protocol("WM_DELETE_WINDOW", on_closing)
    
    # Start the GUI
    root.mainloop()


if __name__ == "__main__":
    main()