import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import chess
import chess.pgn
import numpy as np
from typing import List, Tuple, Dict, Optional
import io
import random
from collections import defaultdict
import math
from pathlib import Path

class ChessDataProcessor:
    """Process PGN files into training data with enhanced features"""
    
    def __init__(self, min_elo: int = 2600, history_length: int = 8):
        self.min_elo = min_elo
        self.history_length = history_length
        self.piece_to_idx = {
            chess.PAWN: 0, chess.KNIGHT: 1, chess.BISHOP: 2,
            chess.ROOK: 3, chess.QUEEN: 4, chess.KING: 5
        }
        
    def board_to_tensor(self, board: chess.Board, history: List[chess.Board]) -> torch.Tensor:
        """Convert board position to enhanced tensor representation"""
        # Total planes: 12*8 (historical pieces) + 4 (attacks) + 2 (pins) + 2 (checks) + 1 (global) = 107
        tensor = torch.zeros(8, 8, 107)
        
        # Historical piece positions (96 planes: 12 pieces * 8 positions)
        boards_to_encode = history[-self.history_length:] + [board]
        boards_to_encode = boards_to_encode[-self.history_length:]  # Ensure exactly 8 boards
        
        for hist_idx, hist_board in enumerate(boards_to_encode):
            for square in chess.SQUARES:
                piece = hist_board.piece_at(square)
                if piece is not None:
                    row, col = divmod(square, 8)
                    piece_idx = self.piece_to_idx[piece.piece_type]
                    color_offset = 0 if piece.color else 6
                    plane_idx = hist_idx * 12 + piece_idx + color_offset
                    tensor[row, col, plane_idx] = 1.0
        
        # Attack maps (4 planes: white/black attacks on white/black pieces)
        white_attacks = self._get_attack_map(board, chess.WHITE)
        black_attacks = self._get_attack_map(board, chess.BLACK)
        
        tensor[:, :, 96] = white_attacks  # White attack map
        tensor[:, :, 97] = black_attacks  # Black attack map
        
        # Tactical features
        pin_mask_white, pin_mask_black = self._get_pin_masks(board)
        tensor[:, :, 98] = pin_mask_white
        tensor[:, :, 99] = pin_mask_black
        
        check_mask, escape_mask = self._get_check_info(board)
        tensor[:, :, 100] = check_mask
        tensor[:, :, 101] = escape_mask
        
        # Global state plane (broadcast across board)
        global_features = self._get_global_features(board)
        tensor[:, :, 102:107] = global_features.unsqueeze(0).unsqueeze(0).expand(8, 8, -1)
        
        return tensor
    
    def _get_attack_map(self, board: chess.Board, color: chess.Color) -> torch.Tensor:
        """Generate attack map for given color"""
        attack_map = torch.zeros(8, 8)
        for square in chess.SQUARES:
            if board.is_attacked_by(color, square):
                row, col = divmod(square, 8)
                attack_map[row, col] = 1.0
        return attack_map
    
    def _get_pin_masks(self, board: chess.Board) -> Tuple[torch.Tensor, torch.Tensor]:
        """Identify pinned pieces for both colors"""
        white_pins = torch.zeros(8, 8)
        black_pins = torch.zeros(8, 8)
        
        for color, pin_tensor in [(chess.WHITE, white_pins), (chess.BLACK, black_pins)]:
            king_square = board.king(color)
            if king_square is not None:
                for square in chess.SQUARES:
                    piece = board.piece_at(square)
                    if piece and piece.color == color:
                        if board.is_pinned(color, square):
                            row, col = divmod(square, 8)
                            pin_tensor[row, col] = 1.0
        
        return white_pins, black_pins
    
    def _get_check_info(self, board: chess.Board) -> Tuple[torch.Tensor, torch.Tensor]:
        """Get check and escape square information"""
        check_mask = torch.zeros(8, 8)
        escape_mask = torch.zeros(8, 8)
        
        if board.is_check():
            # Mark checking pieces
            king_square = board.king(board.turn)
            if king_square is not None:
                attackers = board.attackers(not board.turn, king_square)
                for attacker_square in attackers:
                    row, col = divmod(attacker_square, 8)
                    check_mask[row, col] = 1.0
                
                # Mark escape squares
                king_row, king_col = divmod(king_square, 8)
                for move in board.legal_moves:
                    if move.from_square == king_square:
                        to_row, to_col = divmod(move.to_square, 8)
                        escape_mask[to_row, to_col] = 1.0
        
        return check_mask, escape_mask
    
    def _get_global_features(self, board: chess.Board) -> torch.Tensor:
        """Extract global game state features"""
        features = torch.zeros(5)
        
        # Turn
        features[0] = 1.0 if board.turn == chess.WHITE else 0.0
        
        # Castling rights (4 bits)
        castling = 0
        if board.has_kingside_castling_rights(chess.WHITE):
            castling |= 1
        if board.has_queenside_castling_rights(chess.WHITE):
            castling |= 2
        if board.has_kingside_castling_rights(chess.BLACK):
            castling |= 4
        if board.has_queenside_castling_rights(chess.BLACK):
            castling |= 8
        features[1] = castling / 15.0  # Normalize
        
        # En passant
        features[2] = 1.0 if board.ep_square is not None else 0.0
        
        # Halfmove clock (normalize to [0,1])
        features[3] = min(board.halfmove_clock / 50.0, 1.0)
        
        # Game phase (estimate based on material)
        material_count = len([p for p in board.piece_map().values() 
                            if p.piece_type != chess.PAWN])
        features[4] = min(material_count / 30.0, 1.0)  # Normalize
        
        return features
    
    def move_to_action(self, move: chess.Move) -> int:
        """Convert chess move to action index for factorized policy"""
        from_square = move.from_square
        to_square = move.to_square
        
        # Special handling for promotions
        if move.promotion:
            # Add promotion offset (64*64 = 4096 base moves)
            promotion_offset = 4096 + (move.promotion - 2) * 64 * 64
            return from_square * 64 + to_square + promotion_offset
        
        return from_square * 64 + to_square
    
    def parse_pgn_file(self, pgn_path: str, max_games: Optional[int] = None) -> List[Dict]:
        """Parse PGN file and extract training samples"""
        samples = []
        games_processed = 0
        
        with open(pgn_path, 'r', encoding='utf-8') as pgn_file:
            while True:
                game = chess.pgn.read_game(pgn_file)
                if game is None or (max_games and games_processed >= max_games):
                    break
                
                # Filter by ELO
                white_elo = game.headers.get('WhiteElo', '0')
                black_elo = game.headers.get('BlackElo', '0')
                
                try:
                    if int(white_elo) < self.min_elo or int(black_elo) < self.min_elo:
                        continue
                except (ValueError, TypeError):
                    continue
                
                # Extract game result
                result = game.headers.get('Result', '*')
                if result == '1-0':
                    game_result = 1.0
                elif result == '0-1':
                    game_result = -1.0
                elif result == '1/2-1/2':
                    game_result = 0.0
                else:
                    continue  # Skip unfinished games
                
                # Extract positions and moves
                board = game.board()
                history = []
                move_number = 0
                
                for move in game.mainline_moves():
                    if move_number % 3 == 0:  # Sample every 3rd move to reduce correlation
                        # Get current position
                        position_tensor = self.board_to_tensor(board, history)
                        
                        # Get move target
                        move_action = self.move_to_action(move)
                        
                        # Determine position value based on game result and turn
                        position_value = game_result if board.turn == chess.WHITE else -game_result
                        
                        # Determine game phase for curriculum learning
                        piece_count = len(board.piece_map())
                        if piece_count >= 28:
                            game_phase = 'opening'
                        elif piece_count >= 12:
                            game_phase = 'middlegame'
                        else:
                            game_phase = 'endgame'
                        
                        samples.append({
                            'position': position_tensor,
                            'move': move_action,
                            'value': position_value,
                            'legal_moves': list(board.legal_moves),
                            'game_phase': game_phase,
                            'move_number': move_number,
                            'white_elo': int(white_elo),
                            'black_elo': int(black_elo)
                        })
                    
                    # Update board and history
                    history.append(board.copy())
                    if len(history) > self.history_length:
                        history.pop(0)
                    
                    board.push(move)
                    move_number += 1
                
                games_processed += 1
                if games_processed % 1000 == 0:
                    print(f"Processed {games_processed} games, {len(samples)} samples")
        
        print(f"Total samples extracted: {len(samples)}")
        return samples


class ConvBlock(nn.Module):
    """Convolutional block with batch norm and residual connection"""
    def __init__(self, in_channels: int, out_channels: int, kernel_size: int = 3):
        super().__init__()
        self.conv1 = nn.Conv2d(in_channels, out_channels//4, 1)
        self.conv2 = nn.Conv2d(out_channels//4, out_channels//4, kernel_size, padding=kernel_size//2)
        self.conv3 = nn.Conv2d(out_channels//4, out_channels, 1)
        
        self.bn1 = nn.BatchNorm2d(out_channels//4)
        self.bn2 = nn.BatchNorm2d(out_channels//4)
        self.bn3 = nn.BatchNorm2d(out_channels)
        
        self.shortcut = nn.Sequential()
        if in_channels != out_channels:
            self.shortcut = nn.Sequential(
                nn.Conv2d(in_channels, out_channels, 1),
                nn.BatchNorm2d(out_channels)
            )
    
    def forward(self, x):
        residual = self.shortcut(x)
        
        out = F.relu(self.bn1(self.conv1(x)))
        out = F.relu(self.bn2(self.conv2(out)))
        out = self.bn3(self.conv3(out))
        
        out += residual
        return F.relu(out)


class CBAM(nn.Module):
    """Convolutional Block Attention Module"""
    def __init__(self, channels: int, reduction: int = 16):
        super().__init__()
        
        # Channel attention
        self.channel_attention = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(channels, channels // reduction, 1),
            nn.ReLU(),
            nn.Conv2d(channels // reduction, channels, 1),
            nn.Sigmoid()
        )
        
        # Spatial attention
        self.spatial_attention = nn.Sequential(
            nn.Conv2d(2, 1, 7, padding=3),
            nn.Sigmoid()
        )
    
    def forward(self, x):
        # Channel attention
        ca = self.channel_attention(x)
        x = x * ca
        
        # Spatial attention
        avg_pool = torch.mean(x, dim=1, keepdim=True)
        max_pool, _ = torch.max(x, dim=1, keepdim=True)
        sa_input = torch.cat([avg_pool, max_pool], dim=1)
        sa = self.spatial_attention(sa_input)
        x = x * sa
        
        return x


class TransformerLayer(nn.Module):
    """Transformer layer for long-range dependencies"""
    def __init__(self, d_model: int, nhead: int = 8, dropout: float = 0.1):
        super().__init__()
        self.self_attn = nn.MultiheadAttention(d_model, nhead, dropout=dropout, batch_first=True)
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        self.dropout = nn.Dropout(dropout)
        
        self.ffn = nn.Sequential(
            nn.Linear(d_model, d_model * 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model * 4, d_model),
            nn.Dropout(dropout)
        )
    
    def forward(self, x):
        # Self-attention
        attn_out, _ = self.self_attn(x, x, x)
        x = self.norm1(x + self.dropout(attn_out))
        
        # Feed-forward
        ffn_out = self.ffn(x)
        x = self.norm2(x + ffn_out)
        
        return x


class EnhancedChessNet(nn.Module):
    """Enhanced CNN-Transformer hybrid for chess position evaluation"""
    
    def __init__(self, input_channels: int = 107, hidden_dim: int = 256):
        super().__init__()
        self.hidden_dim = hidden_dim
        
        # Initial convolution
        self.input_conv = nn.Sequential(
            nn.Conv2d(input_channels, hidden_dim, 3, padding=1),
            nn.BatchNorm2d(hidden_dim),
            nn.ReLU()
        )
        
        # Residual tower with attention
        self.res_blocks = nn.ModuleList()
        self.attention_blocks = nn.ModuleList()
        
        for i in range(20):  # 20 residual blocks
            self.res_blocks.append(ConvBlock(hidden_dim, hidden_dim))
            if (i + 1) % 5 == 0:  # Add attention every 5 blocks
                self.attention_blocks.append(CBAM(hidden_dim))
        
        # Spatial to sequence conversion for transformer
        self.spatial_to_seq = nn.Conv2d(hidden_dim, hidden_dim, 1)
        
        # Transformer layers for long-range dependencies
        self.transformer_layers = nn.ModuleList([
            TransformerLayer(hidden_dim) for _ in range(3)
        ])
        
        # Sequence to spatial conversion
        self.seq_to_spatial = nn.Linear(hidden_dim, hidden_dim)
        
        # Policy head (factorized)
        self.from_head = nn.Sequential(
            nn.Conv2d(hidden_dim, 64, 1),
            nn.ReLU(),
            nn.Flatten(),
            nn.Linear(64 * 64, 64),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        self.to_head = nn.Sequential(
            nn.Conv2d(hidden_dim, 64, 1),
            nn.ReLU(),
            nn.Flatten(),
            nn.Linear(64 * 64, 64),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        self.move_scorer = nn.Sequential(
            nn.Linear(128, 64),  # from_features + to_features
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(64, 1)
        )
        
        # Value head
        self.value_head = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Flatten(),
            nn.Linear(hidden_dim, 64),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(32, 1),
            nn.Tanh()
        )
        
        # Auxiliary heads
        self.game_phase_head = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Flatten(),
            nn.Linear(hidden_dim, 32),
            nn.ReLU(),
            nn.Linear(32, 3)  # opening, middlegame, endgame
        )
    
    def forward(self, x, legal_moves_mask=None):
        batch_size = x.size(0)
        
        # Initial convolution
        x = self.input_conv(x.permute(0, 3, 1, 2))  # (B, C, H, W)
        
        # Residual tower with attention
        attention_idx = 0
        for i, res_block in enumerate(self.res_blocks):
            x = res_block(x)
            if (i + 1) % 5 == 0:
                x = self.attention_blocks[attention_idx](x)
                attention_idx += 1
        
        # Spatial features for transformer
        spatial_features = self.spatial_to_seq(x)  # (B, hidden_dim, 8, 8)
        
        # Reshape for transformer: (B, 64, hidden_dim)
        seq_features = spatial_features.view(batch_size, self.hidden_dim, -1).transpose(1, 2)
        
        # Apply transformer layers
        for transformer in self.transformer_layers:
            seq_features = transformer(seq_features)
        
        # Reshape back to spatial: (B, hidden_dim, 8, 8)
        transformed_features = seq_features.transpose(1, 2).view(batch_size, self.hidden_dim, 8, 8)
        
        # Combine original and transformed features
        combined_features = x + transformed_features
        
        # Policy head (factorized)
        from_features = self.from_head(combined_features)  # (B, 64)
        to_features = self.to_head(combined_features)      # (B, 64)
        
        # For each possible move, compute score
        policy_scores = []
        for from_sq in range(64):
            for to_sq in range(64):
                # Create move representation
                move_features = torch.cat([
                    from_features,  # Global from features
                    to_features     # Global to features  
                ], dim=1)
                
                move_score = self.move_scorer(move_features)  # (B, 1)
                policy_scores.append(move_score)
        
        policy_logits = torch.cat(policy_scores, dim=1)  # (B, 4096)
        
        # Apply legal moves mask if provided
        if legal_moves_mask is not None:
            policy_logits = policy_logits + (legal_moves_mask - 1) * 1e9
        
        policy = F.softmax(policy_logits, dim=1)
        
        # Value head
        value = self.value_head(combined_features)
        
        # Auxiliary outputs
        game_phase = self.game_phase_head(combined_features)
        
        return {
            'policy': policy,
            'value': value,
            'game_phase': game_phase,
            'policy_logits': policy_logits
        }


class ChessDataset(Dataset):
    """Dataset for chess training samples with curriculum learning support"""
    
    def __init__(self, samples: List[Dict], phase_filter: Optional[str] = None):
        if phase_filter:
            self.samples = [s for s in samples if s['game_phase'] == phase_filter]
        else:
            self.samples = samples
        
        # Data augmentation: add symmetries
        self.augmented_samples = []
        for sample in self.samples:
            self.augmented_samples.append(sample)
            
            # Add horizontal flip
            flipped_sample = self._flip_sample(sample)
            self.augmented_samples.append(flipped_sample)
    
    def _flip_sample(self, sample: Dict) -> Dict:
        """Apply horizontal flip augmentation"""
        # Flip the position tensor horizontally
        position = sample['position'].clone()
        position = torch.flip(position, [1])  # Flip along width dimension
        
        # Transform move coordinates
        original_move = sample['move']
        if original_move < 4096:  # Regular move
            from_sq = original_move // 64
            to_sq = original_move % 64
            
            # Flip squares horizontally
            from_row, from_col = divmod(from_sq, 8)
            to_row, to_col = divmod(to_sq, 8)
            
            flipped_from_sq = from_row * 8 + (7 - from_col)
            flipped_to_sq = to_row * 8 + (7 - to_col)
            
            flipped_move = flipped_from_sq * 64 + flipped_to_sq
        else:
            flipped_move = original_move  # Keep promotion moves as-is for simplicity
        
        return {
            **sample,
            'position': position,
            'move': flipped_move
        }
    
    def __len__(self):
        return len(self.augmented_samples)
    
    def __getitem__(self, idx):
        sample = self.augmented_samples[idx]
        
        # Create legal moves mask
        legal_moves_mask = torch.zeros(4096)
        for move in sample['legal_moves']:
            move_idx = ChessDataProcessor().move_to_action(move)
            if move_idx < 4096:  # Only handle regular moves for now
                legal_moves_mask[move_idx] = 1.0
        
        # Game phase one-hot encoding
        phase_map = {'opening': 0, 'middlegame': 1, 'endgame': 2}
        game_phase_label = torch.zeros(3)
        game_phase_label[phase_map[sample['game_phase']]] = 1.0
        
        return {
            'position': sample['position'],
            'move': torch.tensor(sample['move'], dtype=torch.long),
            'value': torch.tensor(sample['value'], dtype=torch.float32),
            'legal_moves_mask': legal_moves_mask,
            'game_phase': game_phase_label
        }


class ChessTrainer:
    """Trainer with curriculum learning and advanced optimization"""
    
    def __init__(self, model: EnhancedChessNet, device: torch.device):
        self.model = model.to(device)
        self.device = device
        
        # Optimizer with different learning rates for different components
        policy_params = list(self.model.from_head.parameters()) + \
                       list(self.model.to_head.parameters()) + \
                       list(self.model.move_scorer.parameters())
        
        value_params = list(self.model.value_head.parameters())
        backbone_params = list(self.model.input_conv.parameters()) + \
                         list(self.model.res_blocks.parameters()) + \
                         list(self.model.attention_blocks.parameters()) + \
                         list(self.model.transformer_layers.parameters())
        
        self.optimizer = optim.AdamW([
            {'params': backbone_params, 'lr': 3e-4, 'weight_decay': 1e-4},
            {'params': policy_params, 'lr': 1e-3, 'weight_decay': 1e-4},
            {'params': value_params, 'lr': 5e-4, 'weight_decay': 1e-4}
        ])
        
        # Learning rate scheduler
        self.scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
            self.optimizer, T_0=10, T_mult=2, eta_min=1e-6
        )
        
        # Loss functions
        self.policy_criterion = nn.CrossEntropyLoss(label_smoothing=0.1)
        self.value_criterion = nn.MSELoss()
        self.phase_criterion = nn.CrossEntropyLoss()
    
    def train_epoch(self, dataloader: DataLoader, epoch: int) -> Dict[str, float]:
        """Train for one epoch"""
        self.model.train()
        total_loss = 0
        policy_loss_sum = 0
        value_loss_sum = 0
        phase_loss_sum = 0
        
        for batch_idx, batch in enumerate(dataloader):
            position = batch['position'].to(self.device)
            move_target = batch['move'].to(self.device)
            value_target = batch['value'].to(self.device)
            legal_moves_mask = batch['legal_moves_mask'].to(self.device)
            game_phase_target = batch['game_phase'].to(self.device)
            
            self.optimizer.zero_grad()
            
            # Forward pass
            outputs = self.model(position, legal_moves_mask)
            
            # Compute losses
            policy_loss = self.policy_criterion(outputs['policy_logits'], move_target)
            value_loss = self.value_criterion(outputs['value'].squeeze(), value_target)
            phase_loss = self.phase_criterion(outputs['game_phase'], 
                                            torch.argmax(game_phase_target, dim=1))
            
            # Combined loss with adaptive weights
            total_batch_loss = policy_loss + 0.5 * value_loss + 0.1 * phase_loss
            
            # Backward pass
            total_batch_loss.backward()
            
            # Gradient clipping
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            self.optimizer.step()
            
            # Accumulate losses
            total_loss += total_batch_loss.item()
            policy_loss_sum += policy_loss.item()
            value_loss_sum += value_loss.item()
            phase_loss_sum += phase_loss.item()
            
            if batch_idx % 100 == 0:
                print(f'Epoch {epoch}, Batch {batch_idx}, Loss: {total_batch_loss.item():.4f}')
        
        self.scheduler.step()
        
        return {
            'total_loss': total_loss / len(dataloader),
            'policy_loss': policy_loss_sum / len(dataloader),
            'value_loss': value_loss_sum / len(dataloader),
            'phase_loss': phase_loss_sum / len(dataloader)
        }
    
    def curriculum_train(self, samples: List[Dict], epochs_per_phase: int = 10):
        """Train with curriculum learning: endgame -> middlegame -> opening -> mixed"""
        phases = ['endgame', 'middlegame', 'opening', None]  # None = mixed
        
        for phase in phases:
            print(f"\n{'='*50}")
            print(f"Training on {phase if phase else 'mixed'} positions")
            print(f"{'='*50}")
            
            # Create dataset for current phase
            dataset = ChessDataset(samples, phase_filter=phase)
            dataloader = DataLoader(dataset, batch_size=64, shuffle=True, num_workers=4)
            
            # Train for specified epochs
            for epoch in range(epochs_per_phase):
                losses = self.train_epoch(dataloader, epoch)
                print(f"Epoch {epoch}: Total={losses['total_loss']:.4f}, "
                      f"Policy={losses['policy_loss']:.4f}, "
                      f"Value={losses['value_loss']:.4f}, "
                      f"Phase={losses['phase_loss']:.4f}")
    
    def save_model(self, path: str):
        """Save model checkpoint"""
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict()
        }, path)
    
    def load_model(self, path: str):
        """Load model checkpoint"""
        checkpoint = torch.load(path)
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])


# Example usage
if __name__ == "__main__":
    # Initialize components
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Data processing
    processor = ChessDataProcessor(min_elo=2600)
    
    # Parse PGN files (replace with your PGN file paths)
    print("Processing PGN files...")
    samples = []
    pgn_files = ["grandmaster_games1.pgn", "grandmaster_games2.pgn"]  # Your PGN files
    
    for pgn_file in pgn_files:
        if Path(pgn_file).exists():
            file_samples = processor.parse_pgn_file(pgn_file, max_games=10000)
            samples.extend(file_samples)
        else:
            print(f"Warning: {pgn_file} not found")
    
    if not samples:
        print("No training samples found. Please provide valid PGN files.")
        exit()
    
    print(f"Total training samples: {len(samples)}")
    
    # Initialize model and trainer
    model = EnhancedChessNet(input_channels=107, hidden_dim=256)
    trainer = ChessTrainer(model, device)
    
    print(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # Start curriculum training
    print("Starting curriculum training...")
    trainer.curriculum_train(samples, epochs_per_phase=5)
    
    # Save trained model
    trainer.save_model("enhanced_chessnet.pth")
    print("Model saved successfully!")


class ChessEngine:
    """Chess engine using the trained model for move selection"""
    
    def __init__(self, model_path: str, device: torch.device):
        self.device = device
        self.model = EnhancedChessNet().to(device)
        
        # Load trained weights
        checkpoint = torch.load(model_path, map_location=device)
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.model.eval()
        
        self.processor = ChessDataProcessor()
    
    def get_move(self, board: chess.Board, temperature: float = 0.1) -> chess.Move:
        """Get the best move for current position"""
        with torch.no_grad():
            # Prepare input
            history = []  # In practice, maintain game history
            position_tensor = self.processor.board_to_tensor(board, history)
            position_tensor = position_tensor.unsqueeze(0).to(self.device)
            
            # Create legal moves mask
            legal_moves_mask = torch.zeros(1, 4096).to(self.device)
            legal_moves_list = list(board.legal_moves)
            move_to_idx = {}
            
            for move in legal_moves_list:
                move_idx = self.processor.move_to_action(move)
                if move_idx < 4096:
                    legal_moves_mask[0, move_idx] = 1.0
                    move_to_idx[move_idx] = move
            
            # Get model prediction
            outputs = self.model(position_tensor, legal_moves_mask)
            policy = outputs['policy'][0]  # Remove batch dimension
            value = outputs['value'][0].item()
            
            print(f"Position evaluation: {value:.3f}")
            
            # Select move based on policy
            if temperature == 0:  # Greedy selection
                best_move_idx = torch.argmax(policy).item()
            else:  # Stochastic selection
                policy_temp = F.softmax(torch.log(policy + 1e-8) / temperature, dim=0)
                best_move_idx = torch.multinomial(policy_temp, 1).item()
            
            if best_move_idx in move_to_idx:
                return move_to_idx[best_move_idx]
            else:
                # Fallback to random legal move
                return random.choice(legal_moves_list)
    
    def analyze_position(self, board: chess.Board, top_k: int = 5) -> Dict:
        """Analyze position and return top moves with evaluations"""
        with torch.no_grad():
            history = []
            position_tensor = self.processor.board_to_tensor(board, history)
            position_tensor = position_tensor.unsqueeze(0).to(self.device)
            
            # Create legal moves mask
            legal_moves_mask = torch.zeros(1, 4096).to(self.device)
            legal_moves_list = list(board.legal_moves)
            move_to_idx = {}
            
            for move in legal_moves_list:
                move_idx = self.processor.move_to_action(move)
                if move_idx < 4096:
                    legal_moves_mask[0, move_idx] = 1.0
                    move_to_idx[move_idx] = move
            
            outputs = self.model(position_tensor, legal_moves_mask)
            policy = outputs['policy'][0]
            value = outputs['value'][0].item()
            game_phase = F.softmax(outputs['game_phase'][0], dim=0)
            
            # Get top moves
            top_moves = []
            policy_values, policy_indices = torch.topk(policy, min(top_k, len(move_to_idx)))
            
            for prob, idx in zip(policy_values, policy_indices):
                if idx.item() in move_to_idx:
                    move = move_to_idx[idx.item()]
                    top_moves.append({
                        'move': move.uci(),
                        'probability': prob.item(),
                        'san': board.san(move)
                    })
            
            phase_names = ['Opening', 'Middlegame', 'Endgame']
            game_phase_str = phase_names[torch.argmax(game_phase).item()]
            
            return {
                'position_value': value,
                'game_phase': game_phase_str,
                'game_phase_probs': {
                    phase_names[i]: game_phase[i].item() for i in range(3)
                },
                'top_moves': top_moves
            }


class LightweightMCTS:
    """Lightweight MCTS using the neural network for guidance"""
    
    def __init__(self, model_path: str, device: torch.device, 
                 simulations: int = 100, c_puct: float = 1.0):
        self.engine = ChessEngine(model_path, device)
        self.simulations = simulations
        self.c_puct = c_puct
        
        # MCTS tree storage
        self.q_values = {}      # Q(s,a) - action values
        self.visit_counts = {}  # N(s,a) - visit counts
        self.priors = {}        # P(s,a) - prior probabilities
        self.values = {}        # V(s) - position values
    
    def get_move(self, board: chess.Board) -> chess.Move:
        """Get move using MCTS search"""
        root_key = board.fen()
        
        # Initialize root if not seen
        if root_key not in self.priors:
            self._expand_node(board)
        
        # Perform MCTS simulations
        for _ in range(self.simulations):
            self._simulate(board.copy())
        
        # Select best move based on visit counts
        legal_moves = list(board.legal_moves)
        best_move = None
        best_visits = -1
        
        for move in legal_moves:
            state_action = (root_key, move.uci())
            visits = self.visit_counts.get(state_action, 0)
            if visits > best_visits:
                best_visits = visits
                best_move = move
        
        return best_move if best_move else random.choice(legal_moves)
    
    def _expand_node(self, board: chess.Board):
        """Expand node and get neural network evaluation"""
        state_key = board.fen()
        
        # Get neural network evaluation
        analysis = self.engine.analyze_position(board, top_k=len(list(board.legal_moves)))
        self.values[state_key] = analysis['position_value']
        
        # Store priors for all legal moves
        legal_moves = list(board.legal_moves)
        total_prob = sum(move_info['probability'] for move_info in analysis['top_moves'])
        
        for move in legal_moves:
            move_uci = move.uci()
            # Find probability from analysis
            prob = 0.01  # Default small probability
            for move_info in analysis['top_moves']:
                if move_info['move'] == move_uci:
                    prob = move_info['probability']
                    break
            
            state_action = (state_key, move_uci)
            self.priors[state_action] = prob
            self.q_values[state_action] = 0.0
            self.visit_counts[state_action] = 0
    
    def _simulate(self, board: chess.Board) -> float:
        """Perform one MCTS simulation"""
        if board.is_game_over():
            result = board.result()
            if result == '1-0':
                return 1.0 if board.turn == chess.WHITE else -1.0
            elif result == '0-1':
                return -1.0 if board.turn == chess.WHITE else 1.0
            else:
                return 0.0
        
        state_key = board.fen()
        
        # Expand if not seen
        if state_key not in self.values:
            self._expand_node(board)
            return self.values[state_key] * (1 if board.turn == chess.WHITE else -1)
        
        # Select move using UCB1
        legal_moves = list(board.legal_moves)
        best_move = None
        best_ucb = float('-inf')
        
        total_visits = sum(self.visit_counts.get((state_key, move.uci()), 0) 
                          for move in legal_moves)
        
        for move in legal_moves:
            state_action = (state_key, move.uci())
            q_val = self.q_values[state_action]
            visits = self.visit_counts[state_action]
            prior = self.priors[state_action]
            
            if visits == 0:
                ucb_score = float('inf')
            else:
                exploration = self.c_puct * prior * math.sqrt(total_visits) / (1 + visits)
                ucb_score = q_val + exploration
            
            if ucb_score > best_ucb:
                best_ucb = ucb_score
                best_move = move
        
        # Make move and recurse
        board.push(best_move)
        value = -self._simulate(board)  # Negate for opponent
        board.pop()
        
        # Backup
        state_action = (state_key, best_move.uci())
        self.visit_counts[state_action] += 1
        visits = self.visit_counts[state_action]
        self.q_values[state_action] += (value - self.q_values[state_action]) / visits
        
        return value


def play_game_vs_engine(model_path: str, use_mcts: bool = False):
    """Play a game against the trained model"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    if use_mcts:
        engine = LightweightMCTS(model_path, device, simulations=50)
    else:
        engine = ChessEngine(model_path, device)
    
    board = chess.Board()
    
    print("Starting game! You are white. Enter moves in UCI format (e.g., e2e4)")
    print("Enter 'quit' to exit, 'analyze' to get position analysis")
    print(f"Current position:\n{board}")
    
    while not board.is_game_over():
        if board.turn == chess.WHITE:  # Human's turn
            while True:
                try:
                    user_input = input("Your move: ").strip().lower()
                    
                    if user_input == 'quit':
                        print("Game ended by user.")
                        return
                    elif user_input == 'analyze':
                        if not use_mcts:
                            analysis = engine.analyze_position(board)
                            print(f"\nPosition Analysis:")
                            print(f"Evaluation: {analysis['position_value']:.3f}")
                            print(f"Game Phase: {analysis['game_phase']}")
                            print("Top moves:")
                            for i, move_info in enumerate(analysis['top_moves'][:5], 1):
                                print(f"{i}. {move_info['san']} ({move_info['probability']:.3f})")
                            print()
                        continue
                    
                    move = chess.Move.from_uci(user_input)
                    if move in board.legal_moves:
                        board.push(move)
                        break
                    else:
                        print("Illegal move! Try again.")
                
                except ValueError:
                    print("Invalid format! Use UCI notation (e.g., e2e4)")
        
        else:  # Engine's turn
            print("Engine thinking...")
            if use_mcts:
                engine_move = engine.get_move(board)
            else:
                engine_move = engine.get_move(board, temperature=0.1)
            
            print(f"Engine plays: {engine_move.uci()} ({board.san(engine_move)})")
            board.push(engine_move)
        
        print(f"\nCurrent position:\n{board}")
        
        # Show material and position evaluation
        if not use_mcts:
            analysis = engine.analyze_position(board)
            print(f"Position evaluation: {analysis['position_value']:.3f}")
            print(f"Game phase: {analysis['game_phase']}")
    
    # Game over
    result = board.result()
    print(f"\nGame Over! Result: {result}")
    if result == '1-0':
        print("White wins!")
    elif result == '0-1':
        print("Black wins!")
    else:
        print("Draw!")


def benchmark_model(model_path: str, test_pgn: str, num_positions: int = 1000):
    """Benchmark model accuracy against grandmaster moves"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    engine = ChessEngine(model_path, device)
    processor = ChessDataProcessor(min_elo=2600)
    
    # Load test positions
    test_samples = processor.parse_pgn_file(test_pgn, max_games=100)
    test_samples = random.sample(test_samples, min(num_positions, len(test_samples)))
    
    correct_moves = 0
    top_3_correct = 0
    top_5_correct = 0
    total_positions = len(test_samples)
    
    print(f"Benchmarking on {total_positions} positions...")
    
    for i, sample in enumerate(test_samples):
        # Reconstruct board position
        # Note: This is simplified - in practice, you'd need to reconstruct the full game
        board = chess.Board()  # Placeholder - implement proper position reconstruction
        
        # Get model's top moves
        analysis = engine.analyze_position(board, top_k=5)
        predicted_moves = [move_info['move'] for move_info in analysis['top_moves']]
        
        # Compare with actual grandmaster move
        gm_move_uci = "e2e4"  # Placeholder - extract from sample
        
        if predicted_moves and predicted_moves[0] == gm_move_uci:
            correct_moves += 1
        
        if gm_move_uci in predicted_moves[:3]:
            top_3_correct += 1
        
        if gm_move_uci in predicted_moves[:5]:
            top_5_correct += 1
        
        if (i + 1) % 100 == 0:
            print(f"Processed {i + 1}/{total_positions} positions...")
    
    print(f"\nBenchmark Results:")
    print(f"Top-1 Accuracy: {correct_moves/total_positions:.3f}")
    print(f"Top-3 Accuracy: {top_3_correct/total_positions:.3f}")
    print(f"Top-5 Accuracy: {top_5_correct/total_positions:.3f}")


# Main execution example
if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "train":
            # Training has already been executed above
            pass
            
        elif command == "play":
            model_path = sys.argv[2] if len(sys.argv) > 2 else "enhanced_chessnet.pth"
            use_mcts = len(sys.argv) > 3 and sys.argv[3] == "--mcts"
            play_game_vs_engine(model_path, use_mcts)
            
        elif command == "benchmark":
            model_path = sys.argv[2] if len(sys.argv) > 2 else "enhanced_chessnet.pth"
            test_pgn = sys.argv[3] if len(sys.argv) > 3 else "test_games.pgn"
            benchmark_model(model_path, test_pgn)
            
        else:
            print("Usage:")
            print("  python enhanced_chessnet.py train")
            print("  python enhanced_chessnet.py play [model_path] [--mcts]")
            print("  python enhanced_chessnet.py benchmark [model_path] [test_pgn]")
    else:
        print("Please provide a command: train, play, or benchmark")