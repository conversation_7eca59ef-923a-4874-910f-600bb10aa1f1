import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import threading
import os
import json
import torch
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import chess
import chess.pgn
import random
import datetime
from tqdm import tqdm

# ==================== IMPORT TRAINING LOGIC ====================
# Assume the training logic from the previous code is in a separate module
from chessnet_training import ChessNet, parse_pgn_files, ChessDataset, train_epoch, validate, ChessTrainer

# ==================== GLOBAL VARIABLES ====================
training_thread = None
model = ChessNet()
train_loader = None
val_loader = None
train_losses = []
val_losses = []
is_training = False

# ==================== GUI CLASS ====================
class ChessTrainerGUI(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("ChessNet Trainer")
        self.geometry("800x600")
        
        # Tabs
        self.notebook = ttk.Notebook(self)
        self.training_tab = tk.Frame(self.notebook)
        self.config_tab = tk.Frame(self.notebook)
        self.analysis_tab = tk.Frame(self.notebook)
        self.notebook.add(self.training_tab, text="Training")
        self.notebook.add(self.config_tab, text="Configuration")
        self.notebook.add(self.analysis_tab, text="Analysis")
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # Initialize tabs
        self.create_training_tab()
        self.create_config_tab()
        self.create_analysis_tab()

        # Set default values
        self.set_default_values()

        # Load configuration if exists
        self.load_config()

    # ==================== TRAINING TAB ====================
    def create_training_tab(self):
        # File Selection
        file_frame = tk.LabelFrame(self.training_tab, text="File Selection")
        file_frame.pack(pady=10, padx=10, fill=tk.X)
        
        tk.Label(file_frame, text="Training PGN Folder:").grid(row=0, column=0, sticky=tk.W)
        self.train_pgn_entry = tk.Entry(file_frame, width=50)
        self.train_pgn_entry.grid(row=0, column=1, sticky=tk.W)
        tk.Button(file_frame, text="Browse", command=lambda: self.browse_folder(self.train_pgn_entry)).grid(row=0, column=2)
        
        tk.Label(file_frame, text="Validation PGN Folder:").grid(row=1, column=0, sticky=tk.W)
        self.val_pgn_entry = tk.Entry(file_frame, width=50)
        self.val_pgn_entry.grid(row=1, column=1, sticky=tk.W)
        tk.Button(file_frame, text="Browse", command=lambda: self.browse_folder(self.val_pgn_entry)).grid(row=1, column=2)
        
        # Training Controls
        control_frame = tk.LabelFrame(self.training_tab, text="Controls")
        control_frame.pack(pady=10, padx=10, fill=tk.X)

        self.start_button = tk.Button(control_frame, text="Start Training", command=self.start_training)
        self.start_button.grid(row=0, column=0, padx=5)

        self.stop_button = tk.Button(control_frame, text="Stop Training", command=self.stop_training, state=tk.DISABLED)
        self.stop_button.grid(row=0, column=1, padx=5)

        self.validation_button = tk.Button(control_frame, text="Run Validation", command=self.run_validation)
        self.validation_button.grid(row=0, column=2, padx=5)

        self.checkpoint_button = tk.Button(control_frame, text="Load Checkpoint", command=self.load_checkpoint)
        self.checkpoint_button.grid(row=0, column=3, padx=5)

        self.save_checkpoint_button = tk.Button(control_frame, text="Save Checkpoint", command=self.save_checkpoint_manual)
        self.save_checkpoint_button.grid(row=0, column=4, padx=5)

        self.reset_button = tk.Button(control_frame, text="Reset Progress", command=self.reset_progress)
        self.reset_button.grid(row=0, column=5, padx=5)

        # Second row for additional controls
        self.manage_checkpoints_button = tk.Button(control_frame, text="Manage Checkpoints", command=self.show_checkpoint_manager)
        self.manage_checkpoints_button.grid(row=1, column=0, columnspan=2, padx=5, pady=5, sticky=tk.W+tk.E)
        
        # Progress Bar
        progress_frame = tk.LabelFrame(self.training_tab, text="Progress")
        progress_frame.pack(pady=10, padx=10, fill=tk.X)
        self.progress_label = tk.Label(progress_frame, text="Ready")
        self.progress_label.pack(padx=10, pady=10)
        
        # Loss Plot
        plot_frame = tk.LabelFrame(self.training_tab, text="Training Progress")
        plot_frame.pack(pady=10, padx=10, fill=tk.BOTH, expand=True)
        self.fig = Figure(figsize=(6, 4), dpi=100)
        self.ax = self.fig.add_subplot(111)
        self.ax.set_title("Training and Validation Loss")
        self.ax.set_xlabel("Epochs")
        self.ax.set_ylabel("Loss")
        self.canvas = FigureCanvasTkAgg(self.fig, master=plot_frame)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

    # ==================== CONFIGURATION TAB ====================
    def create_config_tab(self):
        config_frame = tk.LabelFrame(self.config_tab, text="Training Parameters")
        config_frame.pack(pady=10, padx=10, fill=tk.X)
        
        tk.Label(config_frame, text="Epochs:").grid(row=0, column=0, sticky=tk.W)
        self.epochs_entry = tk.Entry(config_frame, width=10)
        self.epochs_entry.grid(row=0, column=1, sticky=tk.W)
        
        tk.Label(config_frame, text="Batch Size:").grid(row=1, column=0, sticky=tk.W)
        self.batch_size_entry = tk.Entry(config_frame, width=10)
        self.batch_size_entry.grid(row=1, column=1, sticky=tk.W)
        
        tk.Label(config_frame, text="Learning Rate:").grid(row=2, column=0, sticky=tk.W)
        self.lr_entry = tk.Entry(config_frame, width=10)
        self.lr_entry.grid(row=2, column=1, sticky=tk.W)
        
        tk.Label(config_frame, text="Weight Decay:").grid(row=3, column=0, sticky=tk.W)
        self.weight_decay_entry = tk.Entry(config_frame, width=10)
        self.weight_decay_entry.grid(row=3, column=1, sticky=tk.W)
        
        tk.Label(config_frame, text="Validation Interval:").grid(row=4, column=0, sticky=tk.W)
        self.val_interval_entry = tk.Entry(config_frame, width=10)
        self.val_interval_entry.grid(row=4, column=1, sticky=tk.W)
        
        tk.Label(config_frame, text="Checkpoint Interval:").grid(row=5, column=0, sticky=tk.W)
        self.checkpoint_interval_entry = tk.Entry(config_frame, width=10)
        self.checkpoint_interval_entry.grid(row=5, column=1, sticky=tk.W)
        
        tk.Label(config_frame, text="Early Stopping Patience:").grid(row=6, column=0, sticky=tk.W)
        self.patience_entry = tk.Entry(config_frame, width=10)
        self.patience_entry.grid(row=6, column=1, sticky=tk.W)
        
        tk.Checkbutton(config_frame, text="Auto-save Best Model", variable=tk.BooleanVar()).grid(row=7, column=0, sticky=tk.W)
        tk.Checkbutton(config_frame, text="Auto-load Latest Checkpoint on Start", variable=tk.BooleanVar()).grid(row=8, column=0, sticky=tk.W)
        tk.Checkbutton(config_frame, text="Enable Time-Based Checkpoints", variable=tk.BooleanVar()).grid(row=9, column=0, sticky=tk.W)
        
        tk.Button(config_frame, text="Apply Configuration", command=self.apply_config).grid(row=10, column=0, columnspan=2, pady=10)

    # ==================== ANALYSIS TAB ====================
    def create_analysis_tab(self):
        analysis_frame = tk.LabelFrame(self.analysis_tab, text="Position Evaluation")
        analysis_frame.pack(pady=10, padx=10, fill=tk.X)
        
        tk.Label(analysis_frame, text="FEN:").grid(row=0, column=0, sticky=tk.W)
        self.fen_entry = tk.Entry(analysis_frame, width=50)
        self.fen_entry.grid(row=0, column=1, sticky=tk.W)
        tk.Button(analysis_frame, text="Evaluate", command=self.evaluate_position).grid(row=0, column=2, padx=5)
        
        self.evaluation_label = tk.Label(analysis_frame, text="Evaluation: Not calculated")
        self.evaluation_label.grid(row=1, column=0, columnspan=3, pady=10)

    # ==================== HELPER FUNCTIONS ====================
    def set_default_values(self):
        """Set default values for all configuration entries"""
        # Set default values to prevent ValueError when fields are empty
        self.epochs_entry.insert(0, "10")
        self.batch_size_entry.insert(0, "32")
        self.lr_entry.insert(0, "0.001")
        self.weight_decay_entry.insert(0, "0.0001")
        self.val_interval_entry.insert(0, "1")
        self.checkpoint_interval_entry.insert(0, "5")
        self.patience_entry.insert(0, "10")

    def browse_folder(self, entry):
        folder = filedialog.askdirectory()
        if folder:
            entry.delete(0, tk.END)
            entry.insert(0, folder)

    def load_config(self):
        try:
            with open("config.json", "r") as f:
                config = json.load(f)
                # Clear and set train/val directories
                self.train_pgn_entry.delete(0, tk.END)
                self.train_pgn_entry.insert(0, config.get("train_pgn_dir", ""))
                self.val_pgn_entry.delete(0, tk.END)
                self.val_pgn_entry.insert(0, config.get("val_pgn_dir", ""))

                # Clear and set configuration values
                self.epochs_entry.delete(0, tk.END)
                self.epochs_entry.insert(0, str(config.get("epochs", 10)))
                self.batch_size_entry.delete(0, tk.END)
                self.batch_size_entry.insert(0, str(config.get("batch_size", 32)))
                self.lr_entry.delete(0, tk.END)
                self.lr_entry.insert(0, str(config.get("learning_rate", 0.001)))
                self.weight_decay_entry.delete(0, tk.END)
                self.weight_decay_entry.insert(0, str(config.get("weight_decay", 0.0001)))
                self.val_interval_entry.delete(0, tk.END)
                self.val_interval_entry.insert(0, str(config.get("validation_interval", 1)))
                self.checkpoint_interval_entry.delete(0, tk.END)
                self.checkpoint_interval_entry.insert(0, str(config.get("checkpoint_interval", 5)))
                self.patience_entry.delete(0, tk.END)
                self.patience_entry.insert(0, str(config.get("early_stopping_patience", 10)))
        except FileNotFoundError:
            pass

    def save_config(self):
        try:
            config = {
                "train_pgn_dir": self.train_pgn_entry.get(),
                "val_pgn_dir": self.val_pgn_entry.get(),
                "epochs": int(self.epochs_entry.get() or "10"),
                "batch_size": int(self.batch_size_entry.get() or "32"),
                "learning_rate": float(self.lr_entry.get() or "0.001"),
                "weight_decay": float(self.weight_decay_entry.get() or "0.0001"),
                "validation_interval": int(self.val_interval_entry.get() or "1"),
                "checkpoint_interval": int(self.checkpoint_interval_entry.get() or "5"),
                "early_stopping_patience": int(self.patience_entry.get() or "10"),
            }
            with open("config.json", "w") as f:
                json.dump(config, f, indent=2)
        except ValueError as e:
            messagebox.showerror("Configuration Error", f"Invalid configuration values: {e}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save configuration: {e}")

    def apply_config(self):
        self.save_config()
        messagebox.showinfo("Configuration", "Configuration applied successfully.")

    def start_training(self):
        global training_thread, is_training
        if not is_training:
            is_training = True
            self.update_controls(is_training=True)
            
            # Get configuration
            train_pgn_dir = self.train_pgn_entry.get()
            val_pgn_dir = self.val_pgn_entry.get()
            epochs = int(self.epochs_entry.get())
            batch_size = int(self.batch_size_entry.get())
            lr = float(self.lr_entry.get())
            weight_decay = float(self.weight_decay_entry.get())
            val_interval = int(self.val_interval_entry.get())
            
            # Prepare data
            train_positions = parse_pgn_files(train_pgn_dir)
            val_positions = parse_pgn_files(val_pgn_dir)
            train_dataset = ChessDataset(train_positions)
            val_dataset = ChessDataset(val_positions)
            global train_loader, val_loader
            train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
            val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
            
            # Start training thread
            training_thread = threading.Thread(target=self.run_training, args=(epochs, lr, weight_decay, val_interval))
            training_thread.start()

    def stop_training(self):
        global is_training
        is_training = False
        self.update_controls(is_training=False)

    def run_training(self, epochs, lr, weight_decay, val_interval):
        global model, train_loader, val_loader, train_losses, val_losses
        
        # Initialize model and optimizer
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        model.to(device)
        optimizer = optim.AdamW(model.parameters(), lr=lr, weight_decay=weight_decay)
        
        for epoch in range(epochs):
            if not is_training:
                break
            
            # Train epoch
            train_loss = train_epoch(model, train_loader, optimizer, device)
            train_losses.append(train_loss)
            
            # Validate
            if (epoch + 1) % val_interval == 0:
                val_loss = validate(model, val_loader, device)
                val_losses.append(val_loss)
                
                # Update plot
                self.update_plot()
            
            # Update progress
            self.progress_label.config(text=f"Epoch {epoch+1}/{epochs}, Train Loss: {train_loss:.4f}")
        
        self.progress_label.config(text="Training Complete!")
        self.update_controls(is_training=False)

    def update_plot(self):
        self.ax.clear()
        self.ax.plot(range(len(train_losses)), train_losses, label="Train Loss")
        self.ax.plot(range(len(val_losses)), val_losses, label="Validation Loss")
        self.ax.set_title("Training and Validation Loss")
        self.ax.set_xlabel("Epochs")
        self.ax.set_ylabel("Loss")
        self.ax.legend()
        self.canvas.draw()

    def update_controls(self, is_training):
        # Enable/disable buttons based on training state
        self.start_button.config(state=tk.DISABLED if is_training else tk.NORMAL)
        self.stop_button.config(state=tk.NORMAL if is_training else tk.DISABLED)
        self.validation_button.config(state=tk.DISABLED if is_training else tk.NORMAL)
        self.checkpoint_button.config(state=tk.DISABLED if is_training else tk.NORMAL)
        self.save_checkpoint_button.config(state=tk.NORMAL if is_training else tk.DISABLED)
        self.reset_button.config(state=tk.DISABLED if is_training else tk.NORMAL)
        self.manage_checkpoints_button.config(state=tk.DISABLED if is_training else tk.NORMAL)

    def run_validation(self):
        global model, val_loader
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        val_loss = validate(model, val_loader, device)
        messagebox.showinfo("Validation", f"Validation Loss: {val_loss:.4f}")

    def load_checkpoint(self):
        global model

        # Create a dialog to choose between latest checkpoint or browse
        choice = messagebox.askyesnocancel(
            "Load Checkpoint",
            "Load latest checkpoint automatically?\n\nYes: Load latest checkpoint\nNo: Browse for checkpoint file\nCancel: Cancel operation"
        )

        if choice is None:  # Cancel
            return

        checkpoint_path = None

        if choice:  # Yes - load latest
            checkpoint_path = ChessTrainer.find_latest_checkpoint("checkpoints/")
            if not checkpoint_path:
                messagebox.showwarning("No Checkpoint", "No checkpoint found in checkpoints/ directory.")
                return
        else:  # No - browse
            checkpoint_path = filedialog.askopenfilename(
                title="Select Checkpoint File",
                defaultextension=".pth",
                filetypes=[("PyTorch files", "*.pth"), ("All files", "*.*")]
            )
            if not checkpoint_path:
                return

        try:
            # Get checkpoint info first
            info = ChessTrainer.get_checkpoint_info(checkpoint_path)
            if 'error' in info:
                messagebox.showerror("Error", f"Failed to read checkpoint: {info['error']}")
                return

            # Show checkpoint info
            info_text = f"""Checkpoint Information:
Epoch: {info.get('epoch', 'Unknown')}
Best Validation Loss: {info.get('best_val_loss', 'Unknown')}
Timestamp: {info.get('timestamp', 'Unknown')}
PyTorch Version: {info.get('pytorch_version', 'Unknown')}

Load this checkpoint?"""

            if not messagebox.askyesno("Confirm Load", info_text):
                return

            # Load just the model state dict for inference
            checkpoint = torch.load(checkpoint_path, map_location='cpu')
            model.load_state_dict(checkpoint['model_state_dict'])

            messagebox.showinfo("Success", f"Checkpoint loaded successfully from epoch {info.get('epoch', 'Unknown')}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to load checkpoint: {str(e)}")

    def save_checkpoint_manual(self):
        """Manually save a checkpoint during training"""
        global model, train_losses, val_losses

        if not hasattr(self, 'training_thread') or not self.training_thread.is_alive():
            messagebox.showwarning("Not Training", "No active training session to save checkpoint from.")
            return

        try:
            # Create a simple checkpoint with current state
            os.makedirs("checkpoints/", exist_ok=True)

            checkpoint = {
                'epoch': len(train_losses),
                'model_state_dict': model.state_dict(),
                'train_losses': train_losses,
                'val_losses': val_losses,
                'timestamp': datetime.now().isoformat(),
                'pytorch_version': torch.__version__,
                'manual_save': True
            }

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            checkpoint_path = f"checkpoints/manual_checkpoint_{timestamp}.pth"

            torch.save(checkpoint, checkpoint_path)
            messagebox.showinfo("Success", f"Manual checkpoint saved: {checkpoint_path}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save checkpoint: {str(e)}")

    def show_checkpoint_manager(self):
        """Show a dialog to manage checkpoints"""
        checkpoint_window = tk.Toplevel(self)
        checkpoint_window.title("Checkpoint Manager")
        checkpoint_window.geometry("600x400")

        # Create listbox to show available checkpoints
        frame = tk.Frame(checkpoint_window)
        frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        tk.Label(frame, text="Available Checkpoints:", font=("Arial", 12, "bold")).pack(anchor=tk.W)

        listbox_frame = tk.Frame(frame)
        listbox_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        scrollbar = tk.Scrollbar(listbox_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        checkpoint_listbox = tk.Listbox(listbox_frame, yscrollcommand=scrollbar.set)
        checkpoint_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.config(command=checkpoint_listbox.yview)

        # Populate listbox with checkpoints
        checkpoint_dir = "checkpoints/"
        if os.path.exists(checkpoint_dir):
            checkpoints = []
            for file in os.listdir(checkpoint_dir):
                if file.endswith('.pth'):
                    file_path = os.path.join(checkpoint_dir, file)
                    info = ChessTrainer.get_checkpoint_info(file_path)
                    if 'error' not in info:
                        display_text = f"{file} (Epoch {info.get('epoch', '?')}, Loss: {info.get('best_val_loss', '?'):.4f})"
                        checkpoints.append((file_path, display_text))

            for _, display_text in sorted(checkpoints, key=lambda x: x[1]):
                checkpoint_listbox.insert(tk.END, display_text)

        # Buttons
        button_frame = tk.Frame(frame)
        button_frame.pack(fill=tk.X, pady=10)

        def load_selected():
            selection = checkpoint_listbox.curselection()
            if selection:
                selected_text = checkpoint_listbox.get(selection[0])
                filename = selected_text.split(' (')[0]
                checkpoint_path = os.path.join(checkpoint_dir, filename)

                try:
                    checkpoint = torch.load(checkpoint_path, map_location='cpu')
                    model.load_state_dict(checkpoint['model_state_dict'])
                    messagebox.showinfo("Success", f"Loaded checkpoint: {filename}")
                    checkpoint_window.destroy()
                except Exception as e:
                    messagebox.showerror("Error", f"Failed to load checkpoint: {str(e)}")

        def delete_selected():
            selection = checkpoint_listbox.curselection()
            if selection:
                selected_text = checkpoint_listbox.get(selection[0])
                filename = selected_text.split(' (')[0]

                if messagebox.askyesno("Confirm Delete", f"Delete checkpoint {filename}?"):
                    try:
                        os.remove(os.path.join(checkpoint_dir, filename))
                        checkpoint_listbox.delete(selection[0])
                        messagebox.showinfo("Success", f"Deleted checkpoint: {filename}")
                    except Exception as e:
                        messagebox.showerror("Error", f"Failed to delete checkpoint: {str(e)}")

        tk.Button(button_frame, text="Load Selected", command=load_selected).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="Delete Selected", command=delete_selected).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="Close", command=checkpoint_window.destroy).pack(side=tk.RIGHT, padx=5)

    def reset_progress(self):
        global train_losses, val_losses
        train_losses = []
        val_losses = []
        self.update_plot()
        self.progress_label.config(text="Ready")

    def evaluate_position(self):
        fen = self.fen_entry.get()
        try:
            board = chess.Board(fen)
            # Create a temporary dataset instance to use the fen_to_tensor method
            temp_dataset = ChessDataset([])
            board_tensor = temp_dataset.fen_to_tensor(board.fen())
            board_tensor = torch.unsqueeze(board_tensor, 0)  # Add batch dimension
            
            device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
            model.eval()
            with torch.no_grad():
                policy_pred, value_pred = model(board_tensor.to(device))
            
            value = value_pred.item()
            self.evaluation_label.config(text=f"Evaluation: {value:.4f}")
        except Exception as e:
            messagebox.showerror("Error", f"Invalid FEN: {str(e)}")

# ==================== MAIN ====================
if __name__ == "__main__":
    app = ChessTrainerGUI()
    app.mainloop()