Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\apps\trainNNIUE\qwe\Qwen_python_20250730_k1tvsz74x.py", line 9, in <module>
    import torch.optim as optim
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\torch\optim\__init__.py", line 9, in <module>
    from torch.optim import lr_scheduler as lr_scheduler, swa_utils as swa_utils
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\torch\optim\lr_scheduler.py", line 22, in <module>
    from torch import inf, Tensor
ImportError: cannot import name 'inf' from 'torch' (unknown location)
