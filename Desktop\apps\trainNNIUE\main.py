import numpy as np
import chess
import chess.pgn
import chess.engine
import torch
import torch.nn as nn
import torch.optim as optim
import pandas as pd
import tkinter as tk
from tkinter import ttk, filedialog, scrolledtext, messagebox
import os
import io
from PIL import Image, ImageTk
import datetime
import threading

def fen_to_bitboard(fen):
    board = chess.Board(fen)
    bitboard = np.zeros(768)  # 12 pieces × 64 squares
    piece_map = {
        "P": 0, "N": 1, "B": 2, "R": 3, "Q": 4, "K": 5,
        "p": 6, "n": 7, "b": 8, "r": 9, "q": 10, "k": 11,
    }
    
    for square in chess.SQUARES:
        piece = board.piece_at(square)
        if piece:
            index = piece_map[piece.symbol()] * 64 + square
            bitboard[index] = 1
    
    return bitboard

# Define NNUE model
class NNUE(nn.Module):
    def __init__(self):
        super(NNUE, self).__init__()
        self.fc1 = nn.Linear(768, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, 1)
    
    def forward(self, x):
        x = torch.relu(self.fc1(x))
        x = torch.relu(self.fc2(x))
        return self.fc3(x)

# Train NNUE model
def train_nnue(model, X_train, y_train, epochs=10, callback=None):
    optimizer = optim.Adam(model.parameters(), lr=0.001)
    loss_fn = nn.MSELoss()
    
    for epoch in range(epochs):
        optimizer.zero_grad()
        predictions = model(X_train)
        loss = loss_fn(predictions, y_train)
        loss.backward()
        optimizer.step()
        if callback:
            callback(f"Epoch {epoch+1}, Loss: {loss.item()}")
        else:
            print(f"Epoch {epoch+1}, Loss: {loss.item()}")

# NNUE prediction function
def predict_evaluation(model, fen):
    bitboard = fen_to_bitboard(fen)
    input_tensor = torch.tensor(bitboard, dtype=torch.float32).unsqueeze(0)
    with torch.no_grad():
        prediction = model(input_tensor)
    return prediction.item()

# Chess GUI class
class ChessNNUEApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Chess NNUE Trainer & Tester")
        self.root.geometry("1200x800")
        
        # Model
        self.model = NNUE()
        self.model_loaded = False
        
        # Stockfish engine
        self.engine = None
        self.engine_path = ""
        
        # Game state
        self.board = chess.Board()
        self.game = chess.pgn.Game()
        self.current_node = self.game
        self.game.headers["Event"] = "NNUE vs Stockfish"
        self.game.headers["Date"] = datetime.datetime.now().strftime("%Y.%m.%d")
        
        # Create tabs
        self.tab_control = ttk.Notebook(root)
        
        self.train_tab = ttk.Frame(self.tab_control)
        self.test_tab = ttk.Frame(self.tab_control)
        self.game_tab = ttk.Frame(self.tab_control)
        
        self.tab_control.add(self.train_tab, text="Train Model")
        self.tab_control.add(self.test_tab, text="Test Model")
        self.tab_control.add(self.game_tab, text="Play Game")
        
        self.tab_control.pack(expand=1, fill="both")
        
        # Set up training tab
        self.setup_training_tab()
        
        # Set up testing tab
        self.setup_testing_tab()
        
        # Set up game tab
        self.setup_game_tab()
        
        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        self.status_bar = ttk.Label(root, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def setup_training_tab(self):
        frame = ttk.LabelFrame(self.train_tab, text="Model Training")
        frame.pack(padx=10, pady=10, fill="both", expand=True)
        
        # Data loading
        ttk.Label(frame, text="Training Data (CSV):").grid(column=0, row=0, padx=10, pady=10, sticky=tk.W)
        self.data_path_var = tk.StringVar()
        ttk.Entry(frame, textvariable=self.data_path_var, width=50).grid(column=1, row=0, padx=10, pady=10, sticky=tk.W)
        ttk.Button(frame, text="Browse...", command=self.browse_data).grid(column=2, row=0, padx=10, pady=10)
        
        # Training parameters
        ttk.Label(frame, text="Epochs:").grid(column=0, row=1, padx=10, pady=10, sticky=tk.W)
        self.epochs_var = tk.IntVar(value=10)
        ttk.Spinbox(frame, from_=1, to=100, textvariable=self.epochs_var, width=5).grid(column=1, row=1, padx=10, pady=10, sticky=tk.W)
        
        # Training actions
        btn_frame = ttk.Frame(frame)
        btn_frame.grid(column=0, row=2, columnspan=3, pady=10)
        
        ttk.Button(btn_frame, text="Train Model", command=self.train_model).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="Save Model", command=self.save_model).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="Load Model", command=self.load_model).pack(side=tk.LEFT, padx=5)
        
        # Log area
        ttk.Label(frame, text="Training Log:").grid(column=0, row=3, padx=10, pady=10, sticky=tk.W)
        self.log_text = scrolledtext.ScrolledText(frame, width=80, height=15)
        self.log_text.grid(column=0, row=4, columnspan=3, padx=10, pady=10)
    
    def setup_testing_tab(self):
        frame = ttk.LabelFrame(self.test_tab, text="Model Testing")
        frame.pack(padx=10, pady=10, fill="both", expand=True)
        
        # FEN input
        ttk.Label(frame, text="FEN String:").grid(column=0, row=0, padx=10, pady=10, sticky=tk.W)
        self.fen_var = tk.StringVar(value="rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1")
        ttk.Entry(frame, textvariable=self.fen_var, width=70).grid(column=1, row=0, columnspan=2, padx=10, pady=10, sticky=tk.W)
        
        # Test buttons
        btn_frame = ttk.Frame(frame)
        btn_frame.grid(column=0, row=1, columnspan=3, pady=10)
        
        ttk.Button(btn_frame, text="Evaluate Position", command=self.evaluate_position).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="Compare with Stockfish", command=self.compare_with_stockfish).pack(side=tk.LEFT, padx=5)
        
        # Stockfish settings
        sf_frame = ttk.LabelFrame(frame, text="Stockfish Settings")
        sf_frame.grid(column=0, row=2, columnspan=3, padx=10, pady=10, sticky=tk.W+tk.E)
        
        ttk.Label(sf_frame, text="Stockfish Path:").grid(column=0, row=0, padx=10, pady=10, sticky=tk.W)
        self.stockfish_path_var = tk.StringVar()
        ttk.Entry(sf_frame, textvariable=self.stockfish_path_var, width=50).grid(column=1, row=0, padx=10, pady=10, sticky=tk.W)
        ttk.Button(sf_frame, text="Browse...", command=self.browse_stockfish).grid(column=2, row=0, padx=10, pady=10)
        
        ttk.Label(sf_frame, text="Depth:").grid(column=0, row=1, padx=10, pady=10, sticky=tk.W)
        self.depth_var = tk.IntVar(value=15)
        ttk.Spinbox(sf_frame, from_=1, to=30, textvariable=self.depth_var, width=5).grid(column=1, row=1, padx=10, pady=10, sticky=tk.W)
        
        # Results area
        ttk.Label(frame, text="Evaluation Results:").grid(column=0, row=3, padx=10, pady=10, sticky=tk.W)
        self.eval_text = scrolledtext.ScrolledText(frame, width=80, height=15)
        self.eval_text.grid(column=0, row=4, columnspan=3, padx=10, pady=10)
    
    def setup_game_tab(self):
        frame = ttk.LabelFrame(self.game_tab, text="NNUE vs Stockfish Game")
        frame.pack(padx=10, pady=10, fill="both", expand=True)
        
        # Game settings
        settings_frame = ttk.LabelFrame(frame, text="Game Settings")
        settings_frame.grid(column=0, row=0, padx=10, pady=10, sticky=tk.W+tk.E)
        
        ttk.Label(settings_frame, text="NNUE Color:").grid(column=0, row=0, padx=10, pady=10, sticky=tk.W)
        self.nnue_color_var = tk.StringVar(value="white")
        ttk.Radiobutton(settings_frame, text="White", variable=self.nnue_color_var, value="white").grid(column=1, row=0, padx=10, pady=10, sticky=tk.W)
        ttk.Radiobutton(settings_frame, text="Black", variable=self.nnue_color_var, value="black").grid(column=2, row=0, padx=10, pady=10, sticky=tk.W)
        
        ttk.Label(settings_frame, text="Time per Move (s):").grid(column=0, row=1, padx=10, pady=10, sticky=tk.W)
        self.time_per_move_var = tk.DoubleVar(value=1.0)
        ttk.Spinbox(settings_frame, from_=0.1, to=60.0, increment=0.1, textvariable=self.time_per_move_var, width=5).grid(column=1, row=1, padx=10, pady=10, sticky=tk.W)
        
        # Game controls
        control_frame = ttk.Frame(frame)
        control_frame.grid(column=0, row=1, padx=10, pady=10)
        
        ttk.Button(control_frame, text="New Game", command=self.new_game).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="Start Game", command=self.start_game).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="Stop Game", command=self.stop_game).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="Export PGN", command=self.export_pgn).pack(side=tk.LEFT, padx=5)
        
        # Board visualization (placeholder)
        board_frame = ttk.LabelFrame(frame, text="Chess Board")
        board_frame.grid(column=0, row=2, padx=10, pady=10, sticky=tk.W+tk.E)
        
        self.board_canvas = tk.Canvas(board_frame, width=400, height=400, bg="white")
        self.board_canvas.pack(padx=10, pady=10)
        
        # Game log
        ttk.Label(frame, text="Game Log:").grid(column=0, row=3, padx=10, pady=10, sticky=tk.W)
        self.game_text = scrolledtext.ScrolledText(frame, width=80, height=10)
        self.game_text.grid(column=0, row=4, padx=10, pady=10)
        
        # Game state variables
        self.game_running = False
        self.game_thread = None
    
    def browse_data(self):
        filename = filedialog.askopenfilename(filetypes=[("CSV Files", "*.csv"), ("All Files", "*.*")])
        if filename:
            self.data_path_var.set(filename)
    
    def browse_stockfish(self):
        if os.name == 'nt':  # Windows
            filename = filedialog.askopenfilename(filetypes=[("Executable Files", "*.exe"), ("All Files", "*.*")])
        else:  # Linux/Mac
            filename = filedialog.askopenfilename()
        
        if filename:
            self.stockfish_path_var.set(filename)
    
    def train_model(self):
        data_path = self.data_path_var.get()
        if not data_path:
            messagebox.showerror("Error", "Please select a training data file")
            return
        
        try:
            # Clear log
            self.log_text.delete(1.0, tk.END)
            self.log_append("Loading training data...")
            
            # Load dataset
            df = pd.read_csv(data_path)
            
            # Convert FENs to bitboard inputs
            self.log_append("Converting FEN positions to bitboards...")
            X = np.array([fen_to_bitboard(fen) for fen in df["FEN"]])
            y = np.array(df["Evaluation"]).reshape(-1, 1)
            
            # Convert to PyTorch tensors
            X_train = torch.tensor(X, dtype=torch.float32)
            y_train = torch.tensor(y, dtype=torch.float32)
            
            # Initialize model if not already done
            if not self.model_loaded:
                self.log_append("Initializing new NNUE model...")
                self.model = NNUE()
            
            # Start training in a separate thread
            self.log_append("Starting training...")
            epochs = self.epochs_var.get()
            
            def training_thread():
                try:
                    train_nnue(self.model, X_train, y_train, epochs=epochs, callback=self.log_append)
                    self.log_append("Training complete!")
                    self.model_loaded = True
                    self.status_var.set("Training complete")
                except Exception as e:
                    self.log_append(f"Error during training: {str(e)}")
                    self.status_var.set("Training failed")
            
            threading.Thread(target=training_thread).start()
            self.status_var.set("Training in progress...")
            
        except Exception as e:
            messagebox.showerror("Error", f"Training failed: {str(e)}")
    
    def save_model(self):
        if not self.model_loaded:
            messagebox.showerror("Error", "No trained model to save")
            return
        
        filename = filedialog.asksaveasfilename(defaultextension=".pt", filetypes=[("PyTorch Models", "*.pt"), ("All Files", "*.*")])
        if filename:
            try:
                torch.save(self.model.state_dict(), filename)
                messagebox.showinfo("Success", "Model saved successfully")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save model: {str(e)}")
    
    def load_model(self):
        filename = filedialog.askopenfilename(filetypes=[("PyTorch Models", "*.pt"), ("All Files", "*.*")])
        if filename:
            try:
                self.model = NNUE()
                self.model.load_state_dict(torch.load(filename))
                self.model.eval()
                self.model_loaded = True
                messagebox.showinfo("Success", "Model loaded successfully")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load model: {str(e)}")
    
    def evaluate_position(self):
        if not self.model_loaded:
            messagebox.showerror("Error", "Please load or train a model first")
            return
        
        fen = self.fen_var.get()
        try:
            # Validate FEN
            chess.Board(fen)
            
            # Get NNUE evaluation
            eval_score = predict_evaluation(self.model, fen)
            
            # Display result
            self.eval_text.delete(1.0, tk.END)
            self.eval_text.insert(tk.END, f"NNUE Evaluation: {eval_score:.2f}\n")
            
        except Exception as e:
            messagebox.showerror("Error", f"Evaluation failed: {str(e)}")
    
    def compare_with_stockfish(self):
        if not self.model_loaded:
            messagebox.showerror("Error", "Please load or train a model first")
            return
        
        stockfish_path = self.stockfish_path_var.get()
        if not stockfish_path:
            messagebox.showerror("Error", "Please set the path to Stockfish engine")
            return
        
        fen = self.fen_var.get()
        try:
            # Validate FEN
            board = chess.Board(fen)
            
            # Get NNUE evaluation
            nnue_eval = predict_evaluation(self.model, fen)
            
            # Get Stockfish evaluation
            depth = self.depth_var.get()
            
            try:
                engine = chess.engine.SimpleEngine.popen_uci(stockfish_path)
                info = engine.analyse(board, chess.engine.Limit(depth=depth))
                stockfish_score = info["score"].white().score(mate_score=10000) / 100.0
                engine.quit()
            except Exception as e:
                self.eval_text.delete(1.0, tk.END)
                self.eval_text.insert(tk.END, f"Error with Stockfish: {str(e)}\n")
                return
            
            # Display comparison
            self.eval_text.delete(1.0, tk.END)
            self.eval_text.insert(tk.END, f"Position: {fen}\n\n")
            self.eval_text.insert(tk.END, f"NNUE Evaluation: {nnue_eval:.2f}\n")
            self.eval_text.insert(tk.END, f"Stockfish Evaluation (depth {depth}): {stockfish_score:.2f}\n\n")
            self.eval_text.insert(tk.END, f"Difference: {abs(nnue_eval - stockfish_score):.2f}\n")
            
        except Exception as e:
            messagebox.showerror("Error", f"Comparison failed: {str(e)}")
    
    def new_game(self):
        if self.game_running:
            messagebox.showerror("Error", "Please stop the current game first")
            return
        
        # Reset board and game
        self.board = chess.Board()
        self.game = chess.pgn.Game()
        self.current_node = self.game
        self.game.headers["Event"] = "NNUE vs Stockfish"
        self.game.headers["Date"] = datetime.datetime.now().strftime("%Y.%m.%d")
        
        # Set players based on NNUE color
        if self.nnue_color_var.get() == "white":
            self.game.headers["White"] = "NNUE"
            self.game.headers["Black"] = "Stockfish"
        else:
            self.game.headers["White"] = "Stockfish"
            self.game.headers["Black"] = "NNUE"
        
        # Clear game log
        self.game_text.delete(1.0, tk.END)
        self.game_text.insert(tk.END, "New game created. Press 'Start Game' to begin play.\n")
        
        # Draw initial board
        self.draw_board()
    
    def start_game(self):
        if self.game_running:
            messagebox.showerror("Error", "Game is already running")
            return
        
        if not self.model_loaded:
            messagebox.showerror("Error", "Please load or train a model first")
            return
        
        stockfish_path = self.stockfish_path_var.get()
        if not stockfish_path:
            messagebox.showerror("Error", "Please set the path to Stockfish engine")
            return
        
        try:
            # Initialize Stockfish engine
            self.engine = chess.engine.SimpleEngine.popen_uci(stockfish_path)
            
            # Start game in a separate thread
            self.game_running = True
            self.game_thread = threading.Thread(target=self.play_game)
            self.game_thread.start()
            
            self.status_var.set("Game in progress...")
            self.game_text.insert(tk.END, "Game started!\n")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to start game: {str(e)}")
    
    def stop_game(self):
        if not self.game_running:
            return
        
        self.game_running = False
        if self.engine:
            self.engine.quit()
            self.engine = None
        
        self.status_var.set("Game stopped")
        self.game_text.insert(tk.END, "Game stopped.\n")
    
    def play_game(self):
        # Set up game parameters
        depth = self.depth_var.get()
        time_per_move = self.time_per_move_var.get()
        nnue_plays_white = self.nnue_color_var.get() == "white"
        
        try:
            while self.game_running and not self.board.is_game_over():
                # Determine whose turn it is
                is_nnue_turn = (self.board.turn == chess.WHITE and nnue_plays_white) or (self.board.turn == chess.BLACK and not nnue_plays_white)
                
                if is_nnue_turn:
                    # NNUE's turn
                    move = self.get_nnue_move()
                else:
                    # Stockfish's turn
                    result = self.engine.play(self.board, chess.engine.Limit(depth=depth, time=time_per_move))
                    move = result.move
                
                # Make the move
                self.board.push(move)
                self.current_node = self.current_node.add_variation(move)
                
                # Log the move
                move_text = f"{'NNUE' if is_nnue_turn else 'Stockfish'} plays: {move.uci()}"
                self.log_game_move(move_text)
                
                # Update the board display
                self.root.after(100, self.draw_board)
                
                # Check if game is over
                if self.board.is_game_over():
                    result = self.board.outcome()
                    if result.winner == chess.WHITE:
                        winner = "NNUE" if nnue_plays_white else "Stockfish"
                    elif result.winner == chess.BLACK:
                        winner = "NNUE" if not nnue_plays_white else "Stockfish"
                    else:
                        winner = "Draw"
                    
                    self.log_game_move(f"Game over. Result: {winner}")
                    self.game.headers["Result"] = self.board.result()
                    break
            
            if not self.game_running:
                self.log_game_move("Game stopped")
            
            # Clean up
            if self.engine:
                self.engine.quit()
                self.engine = None
            
            self.game_running = False
            self.status_var.set("Game finished")
            
        except Exception as e:
            self.log_game_move(f"Error during game: {str(e)}")
            if self.engine:
                self.engine.quit()
                self.engine = None
            self.game_running = False
            self.status_var.set("Game error")
    
    def get_nnue_move(self):
        """Get the best move according to NNUE evaluation"""
        best_move = None
        best_eval = float('-inf') if self.board.turn == chess.WHITE else float('inf')
        
        legal_moves = list(self.board.legal_moves)
        for move in legal_moves:
            self.board.push(move)
            eval_score = predict_evaluation(self.model, self.board.fen())
            
            # Adjust evaluation perspective
            if self.board.turn == chess.WHITE:  # After making move, it's other player's turn
                eval_score = -eval_score
            
            if self.board.turn == chess.WHITE:
                if eval_score > best_eval:
                    best_eval = eval_score
                    best_move = move
            else:
                if eval_score < best_eval:
                    best_eval = eval_score
                    best_move = move
                    
            self.board.pop()
        
        return best_move or legal_moves[0]  # Fallback to first legal move if something goes wrong
    
    def export_pgn(self):
        """Export the current game as PGN"""
        if self.game is None:
            messagebox.showerror("Error", "No game to export")
            return
        
        filename = filedialog.asksaveasfilename(defaultextension=".pgn", filetypes=[("PGN Files", "*.pgn"), ("All Files", "*.*")])
        if filename:
            try:
                with open(filename, "w") as f:
                    print(self.game, file=f)
                messagebox.showinfo("Success", "Game exported successfully")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to export game: {str(e)}")
    
    def draw_board(self):
        """Draw chess board on canvas"""
        # Clear canvas
        self.board_canvas.delete("all")
        
        # Draw board squares
        sq_size = 50
        colors = ["#f0d9b5", "#b58863"]  # Light, Dark
        
        for row in range(8):
            for col in range(8):
                color_idx = (row + col) % 2
                x1, y1 = col * sq_size, row * sq_size
                x2, y2 = x1 + sq_size, y1 + sq_size
                self.board_canvas.create_rectangle(x1, y1, x2, y2, fill=colors[color_idx], outline="")
        
        # Draw pieces (using text representations for simplicity)
        piece_symbols = {
            'P': '♙', 'N': '♘', 'B': '♗', 'R': '♖', 'Q': '♕', 'K': '♔',
            'p': '♟', 'n': '♞', 'b': '♝', 'r': '♜', 'q': '♛', 'k': '♚'
        }
        
        for row in range(8):
            for col in range(8):
                square = chess.square(col, 7-row)  # Convert to chess.Square
                piece = self.board.piece_at(square)
                if piece:
                    x = col * sq_size + sq_size // 2
                    y = row * sq_size + sq_size // 2
                    piece_symbol = piece_symbols[piece.symbol()]
                    color = "black" if piece.color == chess.BLACK else "white"
                    self.board_canvas.create_text(x, y, text=piece_symbol, fill=color, font=("Arial", 24))
    
    def log_append(self, message):
        """Append message to the training log"""
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)
    
    def log_game_move(self, message):
        """Append message to the game log (thread-safe)"""
        self.root.after(0, lambda: self.game_text.insert(tk.END, message + "\n"))
        self.root.after(0, lambda: self.game_text.see(tk.END))

# Main function
def main():
    root = tk.Tk()
    app = ChessNNUEApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
