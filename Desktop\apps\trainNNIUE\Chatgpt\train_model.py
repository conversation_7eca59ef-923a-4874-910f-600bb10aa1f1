import torch
from torch.utils.data import DataLoader
import torch.nn as nn
import torch.optim as optim
from tkinter import Tk, Button, Label, filedialog
from model import ChessCNN
from data_utils import ChessDataset
from move_encoder import MoveEncoder
import os

class TrainerApp:
    def __init__(self, master):
        self.master = master
        self.master.title("Chess Trainer")
        self.encoder = MoveEncoder()

        Label(master, text="Train CNN Chess Model").pack()

        But<PERSON>(master, text="Load PGN and Train", command=self.load_and_train).pack()
        But<PERSON>(master, text="Save Model", command=self.save_model).pack()
        Button(master, text="Load Model", command=self.load_model).pack()
        Button(master, text="Load Stockfish Engine", command=self.load_stockfish).pack()
        Button(master, text="Play vs Stockfish", command=self.play_vs_stockfish).pack()
        But<PERSON>(master, text="Self-Play", command=self.self_play).pack()

        self.model = None
        self.stockfish_path = None

    def load_and_train(self):
        path = filedialog.askopenfilename(filetypes=[("PGN Files", "*.pgn")])
        if not path: return
        dataset = ChessDataset(path, self.encoder)
        loader = DataLoader(dataset, batch_size=32, shuffle=True)

        output_size = self.encoder.get_output_size()
        self.model = ChessCNN(output_size)
        optimizer = optim.Adam(self.model.parameters(), lr=0.001)
        criterion = nn.CrossEntropyLoss()

        for epoch in range(5):
            total_loss = 0
            for X, y in loader:
                optimizer.zero_grad()
                preds = self.model(X)
                loss = criterion(preds, y)
                loss.backward()
                optimizer.step()
                total_loss += loss.item()
            print(f"Epoch {epoch+1}: Loss = {total_loss:.4f}")

    def save_model(self):
        if self.model:
            torch.save(self.model.state_dict(), "chess_cnn_model.pt")
            print("Model saved.")

    def load_model(self):
        output_size = self.encoder.get_output_size()
        self.model = ChessCNN(output_size)
        self.model.load_state_dict(torch.load("chess_cnn_model.pt"))
        self.model.eval()
        print("Model loaded.")

    def load_stockfish(self):
        path = filedialog.askopenfilename(filetypes=[("Engine Executable", "*.exe")])
        if path:
            self.stockfish_path = path
            print(f"Loaded Stockfish from {path}")

    def play_vs_stockfish(self):
        if not self.model or not self.stockfish_path:
            print("Model or Stockfish not loaded.")
            return
        from stockfish_eval import GameManager
        manager = GameManager(self.model, self.encoder, self.stockfish_path)
        result = manager.play_vs_stockfish("vs_stockfish.pgn")
        print(f"Game finished: {result}, saved to vs_stockfish.pgn")

    def self_play(self):
        if not self.model:
            print("Model not loaded.")
            return
        from stockfish_eval import GameManager
        manager = GameManager(self.model, self.encoder)
        result = manager.self_play("selfplay.pgn")
        print(f"Self-play finished: {result}, saved to selfplay.pgn")

if __name__ == "__main__":
    root = Tk()
    app = TrainerApp(root)
    root.mainloop()