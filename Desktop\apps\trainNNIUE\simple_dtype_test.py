#!/usr/bin/env python3

import torch
import torch.nn as nn
import numpy as np

# Simple test to identify the dtype issue
print("Testing PyTorch dtypes...")

# Test 1: Check default tensor creation
print("\n1. Testing tensor creation:")
x1 = torch.tensor([1.0, 2.0, 3.0])
print(f"torch.tensor([1.0, 2.0, 3.0]) dtype: {x1.dtype}")

x2 = torch.FloatTensor([1.0, 2.0, 3.0])
print(f"torch.FloatTensor([1.0, 2.0, 3.0]) dtype: {x2.dtype}")

x3 = torch.tensor([1.0, 2.0, 3.0], dtype=torch.float32)
print(f"torch.tensor(..., dtype=torch.float32) dtype: {x3.dtype}")

# Test 2: Check numpy conversion
print("\n2. Testing numpy conversion:")
np_array = np.array([1.0, 2.0, 3.0], dtype=np.float32)
print(f"numpy array dtype: {np_array.dtype}")

torch_from_numpy = torch.from_numpy(np_array)
print(f"torch.from_numpy(np.float32) dtype: {torch_from_numpy.dtype}")

# Test 3: Simple model
print("\n3. Testing simple model:")
class SimpleModel(nn.Module):
    def __init__(self):
        super().__init__()
        self.linear = nn.Linear(3, 1)
    
    def forward(self, x):
        return self.linear(x)

model = SimpleModel()
print("Model parameter dtypes:")
for name, param in model.named_parameters():
    print(f"  {name}: {param.dtype}")

# Test 4: Force float32
print("\n4. Testing float32 conversion:")
model.float()
print("After model.float():")
for name, param in model.named_parameters():
    print(f"  {name}: {param.dtype}")

# Test 5: Test forward and backward
print("\n5. Testing forward/backward:")
try:
    x = torch.tensor([[1.0, 2.0, 3.0]], dtype=torch.float32)
    y_target = torch.tensor([1.0], dtype=torch.float32)
    
    print(f"Input dtype: {x.dtype}")
    print(f"Target dtype: {y_target.dtype}")
    
    y_pred = model(x)
    print(f"Prediction dtype: {y_pred.dtype}")
    
    loss = nn.MSELoss()(y_pred.squeeze(), y_target)
    print(f"Loss dtype: {loss.dtype}")
    
    loss.backward()
    print("Backward pass successful!")
    
except Exception as e:
    print(f"Error in forward/backward: {e}")
    import traceback
    traceback.print_exc()

print("\nDtype test completed.")
